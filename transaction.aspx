<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="transaction.aspx.cs" Inherits="transaction" %>

<asp:Content ID="Content1" ContentPlaceHolderID="TitleContent" runat="Server">
    交易明细 - <%=uConfig.stcdata("sitename") %>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="head" runat="Server">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* 响应式容器 */
        .transactions-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 15px;
        }

        /* 搜索区域 */
        .search-section {
            background: #fff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #e9ecef;
        }

        .search-wrapper {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 6px;
            padding: 8px 12px;
            border: 1px solid #e9ecef;
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            outline: none;
            font-size: 14px;
            color: #333;
            margin: 0 8px;
        }

        .search-btn {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 6px 16px;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .search-btn:hover {
            background: #1557b0;
        }

        /* 交易表格区域 */
        .transactions-section {
            background: #fff;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .transactions-table-wrapper {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .transactions-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 500;
            padding: 12px 8px;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
            white-space: nowrap;
        }

        .transactions-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
        }

        .transactions-table tr:hover {
            background: #f8f9fa;
        }

        /* 文本截断 */
        .text-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        /* 金额样式 */
        .amount-positive {
            color: #28a745;
            font-weight: 600;
        }

        .amount-negative {
            color: #6c757d;
            font-weight: 600;
        }

        /* 自定义分页样式 */
        .pagination-section {
            padding: 20px 16px;
            text-align: center;
            background: #fff;
            border-radius: 8px;
            margin-top: 16px;
            border: 1px solid #e9ecef;
        }

        .custom-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .pagination-info {
            color: #666;
            font-size: 13px;
            margin-bottom: 12px;
        }

        .page-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
            padding: 0 8px;
            border: 1px solid #e9ecef;
            background: #fff;
            color: #495057;
            text-decoration: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }

        .page-btn:hover:not(.disabled):not(.active) {
            background: #f8f9fa;
            border-color: #dee2e6;
            color: #495057;
            text-decoration: none;
        }

        .page-btn.active {
            background: #1a73e8;
            border-color: #1a73e8;
            color: white;
        }

        .page-btn.disabled {
            background: #f8f9fa;
            border-color: #e9ecef;
            color: #adb5bd;
            cursor: not-allowed;
        }

        .page-btn.nav-btn {
            padding: 0 12px;
        }

        .page-input-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 12px;
        }

        .page-input {
            width: 50px;
            height: 32px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            text-align: center;
            font-size: 13px;
            outline: none;
        }

        .page-input:focus {
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
        }

        .go-btn {
            height: 32px;
            padding: 0 12px;
            border: 1px solid #1a73e8;
            background: #1a73e8;
            color: white;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .go-btn:hover {
            background: #1557b0;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .transactions-container {
                padding: 10px;
            }

            .search-section {
                padding: 12px;
            }

            .search-wrapper {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                margin: 8px 0;
                padding: 8px 0;
            }

            .search-btn {
                margin-top: 8px;
                padding: 10px;
            }

            .transactions-table {
                font-size: 12px;
            }

            .transactions-table th,
            .transactions-table td {
                padding: 8px 4px;
            }

            .text-ellipsis {
                max-width: 120px;
            }

            .pagination-section {
                padding: 16px 12px;
            }

            .custom-pagination {
                gap: 4px;
            }

            .page-btn {
                min-width: 32px;
                height: 32px;
                font-size: 12px;
            }

            .page-btn.nav-btn {
                padding: 0 8px;
            }

            .page-input-group {
                margin: 8px 0;
                flex-basis: 100%;
                justify-content: center;
            }

            .pagination-info {
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .transactions-table {
                font-size: 11px;
            }

            .text-ellipsis {
                max-width: 100px;
            }

            .page-btn {
                min-width: 28px;
                height: 28px;
                font-size: 11px;
            }

            .page-input {
                width: 40px;
                height: 28px;
                font-size: 12px;
            }

            .go-btn {
                height: 28px;
                padding: 0 8px;
                font-size: 11px;
            }
        }
    </style>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="transactions-container">

        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-wrapper">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="#999">
                    <g fill-rule="evenodd" clip-rule="evenodd">
                        <path d="M11.5 18.389c3.875 0 7-3.118 7-6.945 0-3.826-3.125-6.944-7-6.944s-7 3.118-7 6.944 3.125 6.945 7 6.945Zm0 1.5c4.694 0 8.5-3.78 8.5-8.445C20 6.781 16.194 3 11.5 3S3 6.78 3 11.444c0 4.664 3.806 8.445 8.5 8.445Z"></path>
                        <path d="M16.47 16.97a.75.75 0 0 1 1.06 0l3.5 3.5a.75.75 0 1 1-1.06 1.06l-3.5-3.5a.75.75 0 0 1 0-1.06Z"></path>
                    </g>
                </svg>
                <input class="search-input" placeholder="搜索交易ID、类型或备注..." id="search_text">
                <button class="search-btn" onclick="touch()">搜索</button>
            </div>
        </div>

        <!-- 交易表格区域 -->
        <div class="transactions-section">
            <div class="transactions-table-wrapper">
                <table class="transactions-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>业务类型</th>
                            <th>积分变化</th>
                            <th>信息备注</th>
                            <th>流水时间</th>
                        </tr>
                    </thead>
                    <tbody id="list">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-section">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="custom-pagination" id="customPagination"></div>
        </div>


        </div>
    </div>

    <script>
        var clearTableData = function () {
            $('#list').html('');
        }
        var pushTableData = function (data) {
            var tbody = $('#list');
            var tr = $('<tr></tr>');
            tbody.append(tr);

            // 交易ID
            tr.append("<td><strong>" + data.id + "</strong></td>");

            // 业务类型
            tr.append("<td class='text-ellipsis' title='" + data.type + "'>" + data.type + "</td>");

            // 积分变化
            var amountClass = data.dianshu > 0 ? 'amount-positive' : 'amount-negative';
            var amountPrefix = data.dianshu > 0 ? '+' : '';
            tr.append("<td><span class='" + amountClass + "'>" + amountPrefix + data.dianshu + "</span> 点</td>");

            // 信息备注
            tr.append("<td class='text-ellipsis' title='" + data.remark + "'>" + (data.remark || '-') + "</td>");

            // 流水时间
            tr.append("<td><small>" + data.time + "</small></td>");
        }

        var touch_data = {
            data: {
                table: 'flowOrder',
                num: 10,
                index: 0,
            }
        }
        var touch = function () {
            touch_data.data.search_text = $("#search_text").val();
            $.ajax({
                type: 'post',
                dataType: "json",
                url: '<%=unified.apiurl%>list',
                data: touch_data.data,
                success: function (result) {
                    if (result.pager < 0) {
                        result.pager = 100
                    }

                    // 使用自定义分页
                    renderCustomPagination(result.index + 1, result.pager, result.result_list ? result.result_list.length : 0);



                    clearTableData();

                    if (result.status == 0) {

                        for (var i = 0; i < result.result_list.length; i++) {

                            pushTableData(result.result_list[i]);

                        }

                    }
                }
            });
        }
        touch();

        // 自定义分页渲染函数
        function renderCustomPagination(currentPage, totalPages, currentCount) {
            var paginationInfo = $('#paginationInfo');
            var paginationContainer = $('#customPagination');
            var isMobile = $(window).width() <= 768;

            // 显示分页信息
            var startItem = (currentPage - 1) * touch_data.data.num + 1;
            var endItem = startItem + currentCount - 1;
            if (isMobile) {
                paginationInfo.html('第 ' + currentPage + ' / ' + totalPages + ' 页');
            } else {
                paginationInfo.html('显示第 ' + startItem + ' - ' + endItem + ' 项，共 ' + totalPages + ' 页');
            }

            // 清空分页容器
            paginationContainer.empty();

            if (totalPages <= 1) {
                return; // 只有一页或没有数据时不显示分页
            }

            if (isMobile) {
                renderMobilePagination(currentPage, totalPages, paginationContainer);
            } else {
                renderDesktopPagination(currentPage, totalPages, paginationContainer);
            }
        }

        // 移动端分页渲染
        function renderMobilePagination(currentPage, totalPages, container) {
            // 上一页按钮
            var prevBtn = $('<a class="page-btn nav-btn ' + (currentPage === 1 ? 'disabled' : '') + '">‹</a>');
            if (currentPage > 1) {
                prevBtn.click(function() { goToPage(currentPage - 1); });
            }
            container.append(prevBtn);

            // 当前页码显示
            container.append('<span class="page-btn active">' + currentPage + '</span>');

            // 下一页按钮
            var nextBtn = $('<a class="page-btn nav-btn ' + (currentPage === totalPages ? 'disabled' : '') + '">›</a>');
            if (currentPage < totalPages) {
                nextBtn.click(function() { goToPage(currentPage + 1); });
            }
            container.append(nextBtn);

            // 跳转输入框（移动端简化版）
            var jumpGroup = $('<div class="page-input-group"></div>');
            var pageInput = $('<input type="number" class="page-input" min="1" max="' + totalPages + '" placeholder="页码">');
            var goBtn = $('<button class="go-btn">跳转</button>');

            goBtn.click(function() {
                var page = parseInt(pageInput.val());
                if (page >= 1 && page <= totalPages) {
                    goToPage(page);
                    pageInput.val('');
                }
            });

            pageInput.keypress(function(e) {
                if (e.which === 13) { // Enter键
                    goBtn.click();
                }
            });

            jumpGroup.append(pageInput);
            jumpGroup.append(goBtn);
            container.append(jumpGroup);
        }

        // 桌面端分页渲染
        function renderDesktopPagination(currentPage, totalPages, container) {
            // 首页按钮
            var firstBtn = $('<a class="page-btn nav-btn ' + (currentPage === 1 ? 'disabled' : '') + '">首页</a>');
            if (currentPage > 1) {
                firstBtn.click(function() { goToPage(1); });
            }
            container.append(firstBtn);

            // 上一页按钮
            var prevBtn = $('<a class="page-btn nav-btn ' + (currentPage === 1 ? 'disabled' : '') + '">上一页</a>');
            if (currentPage > 1) {
                prevBtn.click(function() { goToPage(currentPage - 1); });
            }
            container.append(prevBtn);

            // 页码按钮
            var startPage = Math.max(1, currentPage - 2);
            var endPage = Math.min(totalPages, currentPage + 2);

            // 如果开始页码大于1，显示省略号
            if (startPage > 1) {
                container.append('<a class="page-btn" onclick="goToPage(1)">1</a>');
                if (startPage > 2) {
                    container.append('<span class="page-btn disabled">...</span>');
                }
            }

            // 显示页码
            for (var i = startPage; i <= endPage; i++) {
                var pageBtn = $('<a class="page-btn ' + (i === currentPage ? 'active' : '') + '">' + i + '</a>');
                if (i !== currentPage) {
                    pageBtn.attr('onclick', 'goToPage(' + i + ')');
                }
                container.append(pageBtn);
            }

            // 如果结束页码小于总页数，显示省略号
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    container.append('<span class="page-btn disabled">...</span>');
                }
                container.append('<a class="page-btn" onclick="goToPage(' + totalPages + ')">' + totalPages + '</a>');
            }

            // 下一页按钮
            var nextBtn = $('<a class="page-btn nav-btn ' + (currentPage === totalPages ? 'disabled' : '') + '">下一页</a>');
            if (currentPage < totalPages) {
                nextBtn.click(function() { goToPage(currentPage + 1); });
            }
            container.append(nextBtn);

            // 末页按钮
            var lastBtn = $('<a class="page-btn nav-btn ' + (currentPage === totalPages ? 'disabled' : '') + '">末页</a>');
            if (currentPage < totalPages) {
                lastBtn.click(function() { goToPage(totalPages); });
            }
            container.append(lastBtn);

            // 跳转输入框
            var jumpGroup = $('<div class="page-input-group"></div>');
            jumpGroup.append('<span style="font-size: 12px; color: #666;">跳转到</span>');
            var pageInput = $('<input type="number" class="page-input" min="1" max="' + totalPages + '" placeholder="' + currentPage + '">');
            var goBtn = $('<button class="go-btn">GO</button>');

            goBtn.click(function() {
                var page = parseInt(pageInput.val());
                if (page >= 1 && page <= totalPages) {
                    goToPage(page);
                }
            });

            pageInput.keypress(function(e) {
                if (e.which === 13) { // Enter键
                    goBtn.click();
                }
            });

            jumpGroup.append(pageInput);
            jumpGroup.append(goBtn);
            container.append(jumpGroup);
        }

        // 跳转到指定页面
        function goToPage(page) {
            touch_data.data.index = page - 1;
            touch();
        }

        // 窗口大小变化时重新渲染分页
        $(window).resize(function() {
            var paginationContainer = $('#customPagination');
            if (paginationContainer.children().length > 0) {
                // 如果分页已经渲染，重新渲染以适应新的屏幕尺寸
                var currentPage = parseInt($('.page-btn.active').text()) || 1;
                var totalPages = parseInt($('.page-input').attr('max')) || 1;
                var currentCount = $('#list tr').length;
                renderCustomPagination(currentPage, totalPages, currentCount);
            }
        });
    </script>
</asp:Content>

