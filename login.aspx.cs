using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;

public partial class user_login : globalClass
{
    protected void Page_Load(object sender, EventArgs e)
    {

        fuzhu fz = new fuzhu(HttpContext.Current);
        List<SqlParameter> pams = fz.collectReqParames();
        fz.setResponseLable("msg");
        if (fz.req("do") == "admin_login")
        {
            string sql = string.Empty;
            DataTable dt = new DataTable();
            dbClass db = new dbClass();
            Dictionary<string, object> dic = new Dictionary<string, object>();

            if (fz.req("refer").IndexOf("/mgr_data/") != -1 || fz.req("login") == "admin")
            {

                sql = "  select * from account with(nolock) where nick=@username and pwd=@password ";
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count > 0)
                {
                    newCookie(uConfig.loginParames_nick_admin, dt.Rows[0]["nick"] + "");
                    newCookie(uConfig.loginParames_pwd_admin, dt.Rows[0]["pwd"] + "");
                    if ((dt.Rows[0]["nick"] + "").IndexOf("hz-") != -1)
                    {

                    }
                    dic.Add("redirect_url", "../mgr_data/_index.aspx");

                    fz.sendResponse("登录成功", 1, dic);
                }
                else
                {
                    fz.sendResponse("账号或密码有误");
                }
            }
            else
            {
                sql = " declare @uid int update users set @uid=id,loginTime='1900-01-01' output deleted.* where status=1 and nick=@username and pwd=@password  ";
                dt = db.getDataTable(sql, pams.ToArray());
                if (dt.Rows.Count > 0)
                {
                    newCookie(uConfig.loginParames_nick, dt.Rows[0]["nick"] + "");
                    newCookie(uConfig.loginParames_pwd, dt.Rows[0]["pwd"] + "");
                    newCookie(uConfig.loginParames_uid, dt.Rows[0]["id"] + "");
                    newCookie(uConfig.loginParames_usp, dt.Rows[0]["sp"] + "");
                    newCookie(uConfig.loginParames_utp, dt.Rows[0]["userType"] + "");
                    dic.Add("redirect_url", "../index.aspx");
                    fz.sendResponse("登录成功", 1, dic);
                }
                else
                {
                    fz.sendResponse("您输入的账号或者密码有误！");
                }
            }


        }
    }
}