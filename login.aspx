<%@ Page Language="C#" AutoEventWireup="true" CodeFile="login.aspx.cs" Inherits="serv_login" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <title>登录 - <%=uConfig.stcdata("sitename") %></title>
    <script src="demo/js/jquery.min.js"></script>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }

        /* 背景动画 */
        .background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: -2;
        }

        .background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        /* 主容器 */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        /* 登录卡片 */
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 头部区域 */
        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            border-radius: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .logo svg {
            width: 32px;
            height: 32px;
            fill: white;
        }

        .login-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .login-subtitle {
            font-size: 14px;
            color: #666;
        }

        .admin-badge {
            display: inline-block;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        /* 表单区域 */
        .login-form {
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-input {
            width: 100%;
            height: 50px;
            padding: 0 16px 0 48px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 16px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-input::placeholder {
            color: #adb5bd;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            color: #adb5bd;
            transition: color 0.3s ease;
        }

        .form-group:focus-within .input-icon {
            color: #667eea;
        }

        /* 记住我选项 */
        .remember-section {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .checkbox-wrapper {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }

        .checkbox-input {
            display: none;
        }

        .checkbox-custom {
            width: 18px;
            height: 18px;
            border: 2px solid #e9ecef;
            border-radius: 4px;
            margin-right: 8px;
            position: relative;
            transition: all 0.3s ease;
        }

        .checkbox-input:checked + .checkbox-custom {
            background: #667eea;
            border-color: #667eea;
        }

        .checkbox-input:checked + .checkbox-custom::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .checkbox-label {
            font-size: 14px;
            color: #666;
        }

        /* 登录按钮 */
        .login-button {
            width: 100%;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-button:hover:not(.loading) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button.loading {
            cursor: not-allowed;
            opacity: 0.8;
        }

        .button-text {
            transition: opacity 0.3s ease;
        }

        .button-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .login-button.loading .button-text {
            opacity: 0;
        }

        .login-button.loading .button-loading {
            opacity: 1;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 底部 */
        .login-footer {
            text-align: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e9ecef;
        }

        .footer-text {
            font-size: 12px;
            color: #adb5bd;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .login-container {
                padding: 15px;
            }

            .login-card {
                padding: 30px 24px;
                border-radius: 16px;
                max-width: none;
            }

            .login-title {
                font-size: 22px;
            }

            .form-input {
                height: 48px;
                font-size: 16px; /* 防止iOS缩放 */
            }

            .login-button {
                height: 48px;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 10px;
            }

            .login-card {
                padding: 24px 20px;
                border-radius: 12px;
            }

            .logo {
                width: 56px;
                height: 56px;
            }

            .logo svg {
                width: 28px;
                height: 28px;
            }

            .login-title {
                font-size: 20px;
            }
        }

        /* 错误提示 */
        .error-message {
            background: #fee;
            border: 1px solid #fcc;
            color: #c33;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .error-message.show {
            display: block;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">


        <div id="box"></div>
        <div class="cent-box">
            <div class="cent-box-header">

                <%=(HttpContext.Current.Request.UrlReferrer+"").IndexOf("/mgr_data/") != -1 ? "<svg viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4889' width='64' height='64'><path d='M815.020408 1024H208.979592C94.040816 1024 0 929.959184 0 815.020408V208.979592C0 94.040816 94.040816 0 208.979592 0h606.040816c114.938776 0 208.979592 94.040816 208.979592 208.979592v606.040816c0 114.938776-94.040816 208.979592-208.979592 208.979592z' fill='#257CFF' p-id='4890'></path><path d='M731.428571 815.020408H292.571429c-45.97551 0-83.591837-37.616327-83.591837-83.591837v-20.897959c0-103.444898 84.636735-188.081633 188.081632-188.081632h229.877552c103.444898 0 188.081633 84.636735 188.081632 188.081632v20.897959c0 45.97551-37.616327 83.591837-83.591837 83.591837z' fill='#FFFFFF' opacity='.6' p-id='4891'></path><path d='M720.979592 773.22449H303.020408c-29.257143 0-52.244898-22.987755-52.244898-52.244898 0-86.726531 70.008163-156.734694 156.734694-156.734694h208.979592c86.726531 0 156.734694 70.008163 156.734694 156.734694 0 29.257143-22.987755 52.244898-52.244898 52.244898z' fill='#FFFFFF' p-id='4892'></path><path d='M512 522.44898c-86.726531 0-156.734694-70.008163-156.734694-156.734694s70.008163-156.734694 156.734694-156.734694 156.734694 70.008163 156.734694 156.734694-70.008163 156.734694-156.734694 156.734694z' fill='#FFFFFF' opacity='.88' p-id='4893'></path><path d='M512 470.204082c-57.469388 0-104.489796-47.020408-104.489796-104.489796s47.020408-104.489796 104.489796-104.489796 104.489796 47.020408 104.489796 104.489796-47.020408 104.489796-104.489796 104.489796z' fill='#FFFFFF' p-id='4894'></path><path d='M544.391837 564.244898h-62.693878L459.755102 689.632653l52.244898 41.795918 52.244898-41.795918c-6.269388-41.795918-13.583673-83.591837-19.853061-125.387755z' fill='#257CFF' p-id='4895'></path></svg>" : "<svg t='1688413826839' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='946' width='64' height='64'>                <path d='M298.666667 896c141.397333 0 256-115.968 256-259.029333C554.666667 541.568 469.333333 371.925333 298.666667 128c-170.666667 243.925333-256 413.568-256 508.970667C42.666667 780.032 157.269333 896 298.666667 896z' fill='#FFCCCC' p-id='947'></path><path d='M554.666667 85.333333c235.648 0 426.666667 191.018667 426.666666 426.666667s-191.018667 426.666667-426.666666 426.666667c-66.986667 0-131.84-15.488-190.464-44.8a42.666667 42.666667 0 0 1 38.144-76.330667 341.333333 341.333333 0 1 0 1.834666-612.010667 42.666667 42.666667 0 1 1-37.717333-76.544A425.088 425.088 0 0 1 554.666667 85.333333z m0 256a170.666667 170.666667 0 1 1 0 341.333334 170.666667 170.666667 0 0 1 0-341.333334z m0 85.333334a85.333333 85.333333 0 1 0 0 170.666666 85.333333 85.333333 0 0 0 0-170.666666z' fill='#363637' p-id='948'></path></svg>" %>


                <h2 class="sub-title"><%=(HttpContext.Current.Request.UrlReferrer+"").IndexOf("/mgr_data/") != -1 || Request.QueryString["login"]+""=="admin" ? "<span style='color:red;'>管理员登录</span>" : "客户登录" %></h2>
            </div>

            <div class="cont-main clearfix">
                <div class="index-tab">
                    <div class="index-slide-nav">
                        <a class="active">登录</a>
                        <!--<a href="register.html">注册</a>-->
                        <div class="slide-bar"></div>
                    </div>
                </div>

                <div class="login form">
                    <div class="group">
                        <div class="group-ipt email">
                            <input type="text" name="username" id="username" class="ipt" placeholder="账号" required>
                        </div>
                        <div class="group-ipt password">
                            <input type="password" name="password" id="password" class="ipt" placeholder="密码" required>
                        </div>
                    </div>
                </div>

                <div class="button">
                    <button type="button" class="login-btn register-btn" id="button">登录</button>
                </div>

                <div class="remember clearfix">
                    <label class="remember-me">
                        <span class="icon"><span class="zt"></span></span>
                        <input type="checkbox" name="remember-me" id="remember-me" class="remember-mecheck" checked>记住我</label>
                    <%--<label class="forgot-password">
                    <a href="#">忘记密码？</a>
                </label>--%>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Designed By HangTong & DT</p>
        </div>

        <script src='demo/js/particles.js' type="text/javascript"></script>
        <script src='demo/js/background.js' type="text/javascript"></script>


        <script>
            $("#button").on("click", function () {
                var data = {};
                data["do"] = 'admin_login';
                data["login"] = '<%=Request.QueryString["login"] + "" %>';
                data["username"] = $("#username").val();
                data["password"] = $("#password").val();
                data["refer"] = document.referrer + '';
                $.ajax({
                    type: "POST",
                    data: data,
                    datatype: "json",
                    success: function (json) {
                        if (json.code == 1) {
                            location.href = json.redirect_url;
                        } else {
                            alert(json.msg);
                        }
                    },
                    error: function () {
                    }
                });
            })
        </script>

    </form>
</body>




</html>
