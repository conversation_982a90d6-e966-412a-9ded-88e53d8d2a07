<%@ Page Language="C#" AutoEventWireup="true" CodeFile="login.aspx.cs" Inherits="serv_login" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <title>登录 - <%=uConfig.stcdata("sitename") %></title>
    <script src="demo/js/jquery.min.js"></script>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }

        /* 背景动画 */
        .background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: -2;
            /* 确保背景不会阻止点击事件 */
            pointer-events: none;
        }

        .background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        /* 主容器 */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        /* 登录卡片 */
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideUp 0.6s ease-out;
            position: relative;
            z-index: 10;
            /* 确保移动端可以点击 */
            -webkit-touch-callout: default;
            -webkit-user-select: text;
            -khtml-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            user-select: text;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 头部区域 */
        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            border-radius: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .logo svg {
            width: 32px;
            height: 32px;
            fill: white;
        }

        .login-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .login-subtitle {
            font-size: 14px;
            color: #666;
        }

        .admin-badge {
            display: inline-block;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        /* 表单区域 */
        .login-form {
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-input {
            width: 100%;
            height: 50px;
            padding: 0 16px 0 48px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 16px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            outline: none;
            /* 移动端优化 */
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }

        .form-input:focus {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-input::placeholder {
            color: #adb5bd;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            color: #adb5bd;
            transition: color 0.3s ease;
        }

        .form-group:focus-within .input-icon {
            color: #667eea;
        }

        /* 记住我选项 */
        .remember-section {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .checkbox-wrapper {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
            /* 移动端优化 */
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            padding: 4px; /* 增加触摸区域 */
            margin: -4px; /* 抵消padding */
        }

        .checkbox-input {
            display: none;
        }

        .checkbox-custom {
            width: 18px;
            height: 18px;
            border: 2px solid #e9ecef;
            border-radius: 4px;
            margin-right: 8px;
            position: relative;
            transition: all 0.3s ease;
        }

        .checkbox-input:checked + .checkbox-custom {
            background: #667eea;
            border-color: #667eea;
        }

        .checkbox-input:checked + .checkbox-custom::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .checkbox-label {
            font-size: 14px;
            color: #666;
        }

        /* 登录按钮 */
        .login-button {
            width: 100%;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            /* 移动端优化 */
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .login-button:hover:not(.loading) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button.loading {
            cursor: not-allowed;
            opacity: 0.8;
        }

        .button-text {
            transition: opacity 0.3s ease;
        }

        .button-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .login-button.loading .button-text {
            opacity: 0;
        }

        .login-button.loading .button-loading {
            opacity: 1;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 底部 */
        .login-footer {
            text-align: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e9ecef;
        }

        .footer-text {
            font-size: 12px;
            color: #adb5bd;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .login-container {
                padding: 15px;
                /* 确保容器可以滚动 */
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
            }

            .login-card {
                padding: 30px 24px;
                border-radius: 16px;
                max-width: none;
                /* 移动端特殊优化 */
                min-height: auto;
                position: relative;
                z-index: 100;
            }

            .login-title {
                font-size: 22px;
            }

            .form-input {
                height: 48px;
                font-size: 16px; /* 防止iOS缩放 */
                /* 移动端输入框优化 */
                -webkit-text-size-adjust: 100%;
                -ms-text-size-adjust: 100%;
                text-size-adjust: 100%;
            }

            .login-button {
                height: 48px;
                /* 移动端按钮优化 */
                min-height: 48px;
                line-height: 48px;
            }

            /* 移动端特殊处理 */
            .checkbox-wrapper {
                min-height: 44px; /* iOS推荐的最小触摸目标 */
                padding: 8px;
                margin: -8px;
            }

            .checkbox-custom {
                width: 20px;
                height: 20px;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 10px;
            }

            .login-card {
                padding: 24px 20px;
                border-radius: 12px;
            }

            .logo {
                width: 56px;
                height: 56px;
            }

            .logo svg {
                width: 28px;
                height: 28px;
            }

            .login-title {
                font-size: 20px;
            }
        }

        /* 错误提示 */
        .error-message {
            background: #fee;
            border: 1px solid #fcc;
            color: #c33;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .error-message.show {
            display: block;
        }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <!-- 背景 -->
        <div class="background"></div>

        <!-- 登录容器 -->
        <div class="login-container">
            <div class="login-card">
                <!-- 头部 -->
                <div class="login-header">
                    <div class="logo">
                        <%if ((HttpContext.Current.Request.UrlReferrer+"").IndexOf("/mgr_data/") != -1 || Request.QueryString["login"]+""=="admin") { %>
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 21H5V3H13V9H19Z"/>
                            </svg>
                        <%} else { %>
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 5C13.66 5 15 6.34 15 8S13.66 11 12 11 9 9.66 9 8 10.34 5 12 5ZM12 19.2C9.5 19.2 7.29 17.92 6 15.98C6.03 13.99 10 12.9 12 12.9S17.97 13.99 18 15.98C16.71 17.92 14.5 19.2 12 19.2Z"/>
                            </svg>
                        <%} %>
                    </div>
                    <h1 class="login-title">
                        欢迎回来
                        <%if ((HttpContext.Current.Request.UrlReferrer+"").IndexOf("/mgr_data/") != -1 || Request.QueryString["login"]+""=="admin") { %>
                            <span class="admin-badge">管理员</span>
                        <%} %>
                    </h1>
                    <p class="login-subtitle">请登录您的账户以继续</p>
                </div>

                <!-- 错误提示 -->
                <div class="error-message" id="errorMessage"></div>

                <!-- 登录表单 -->
                <div class="login-form">
                    <div class="form-group">
                        <div class="input-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 5C13.66 5 15 6.34 15 8S13.66 11 12 11 9 9.66 9 8 10.34 5 12 5ZM12 19.2C9.5 19.2 7.29 17.92 6 15.98C6.03 13.99 10 12.9 12 12.9S17.97 13.99 18 15.98C16.71 17.92 14.5 19.2 12 19.2Z"/>
                            </svg>
                        </div>
                        <input type="text" name="username" id="username" class="form-input" placeholder="请输入用户名" required autocomplete="username">
                    </div>

                    <div class="form-group">
                        <div class="input-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                            </svg>
                        </div>
                        <input type="password" name="password" id="password" class="form-input" placeholder="请输入密码" required autocomplete="current-password">
                    </div>
                </div>

                <!-- 记住我 -->
                <div class="remember-section">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" name="remember-me" id="remember-me" class="checkbox-input" checked>
                        <div class="checkbox-custom"></div>
                        <span class="checkbox-label">记住我</span>
                    </label>
                </div>

                <!-- 登录按钮 -->
                <button type="button" class="login-button" id="loginButton">
                    <span class="button-text">登录</span>
                    <div class="button-loading">
                        <div class="spinner"></div>
                    </div>
                </button>

                <!-- 底部 -->
                <div class="login-footer">
                    <p class="footer-text">Powered by <%=uConfig.stcdata("sitename") %></p>
                </div>
            </div>
        </div>

        <script>
            // 移动端检测和初始化
            $(document).ready(function() {
                // 检测是否为移动设备
                var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

                if (isMobile) {
                    // 移动端特殊处理
                    $('body').addClass('mobile-device');

                    // 禁用iOS的自动缩放
                    $('meta[name=viewport]').attr('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');

                    // 移动端输入框焦点处理
                    $('.form-input').on('focus', function() {
                        setTimeout(function() {
                            window.scrollTo(0, 0);
                        }, 100);
                    });

                    // 调试：添加点击测试
                    $('.login-card').on('click', function(e) {
                        console.log('Login card clicked:', e.target);
                    });

                    $('.login-button').on('click', function(e) {
                        console.log('Login button clicked');
                    });
                }
            });

            // 登录功能
            $("#loginButton").on("click", function () {
                var $button = $(this);
                var $errorMsg = $('#errorMessage');

                // 获取表单数据
                var username = $("#username").val().trim();
                var password = $("#password").val().trim();

                // 验证输入
                if (!username) {
                    showError('请输入用户名');
                    $("#username").focus();
                    return;
                }

                if (!password) {
                    showError('请输入密码');
                    $("#password").focus();
                    return;
                }

                // 显示加载状态
                $button.addClass('loading');
                $errorMsg.removeClass('show');

                var data = {};
                data["do"] = 'admin_login';
                data["login"] = '<%=Request.QueryString["login"] + "" %>';
                data["username"] = username;
                data["password"] = password;
                data["refer"] = document.referrer + '';

                $.ajax({
                    type: "POST",
                    data: data,
                    datatype: "json",
                    timeout: 10000,
                    success: function (json) {
                        if (json.code == 1) {
                            // 登录成功
                            $button.find('.button-text').text('登录成功');
                            setTimeout(function() {
                                location.href = json.redirect_url;
                            }, 500);
                        } else {
                            // 登录失败
                            $button.removeClass('loading');
                            showError(json.msg || '登录失败，请检查用户名和密码');
                        }
                    },
                    error: function(xhr, status, error) {
                        $button.removeClass('loading');
                        if (status === 'timeout') {
                            showError('请求超时，请检查网络连接');
                        } else {
                            showError('网络错误，请稍后重试');
                        }
                    }
                });
            });

            // 显示错误信息
            function showError(message) {
                var $errorMsg = $('#errorMessage');
                $errorMsg.text(message).addClass('show');
                setTimeout(function() {
                    $errorMsg.removeClass('show');
                }, 5000);
            }

            // 回车键登录
            $(document).keypress(function(e) {
                if (e.which === 13) {
                    $("#loginButton").click();
                }
            });

            // 移动端触摸优化 - 完全重写
            if ('ontouchstart' in window) {
                // 移动端专用事件处理
                var touchStartTime = 0;
                var touchStartTarget = null;

                // 记录触摸开始
                $(document).on('touchstart', function(e) {
                    touchStartTime = Date.now();
                    touchStartTarget = e.target;
                });

                // 处理触摸结束
                $(document).on('touchend', function(e) {
                    var touchDuration = Date.now() - touchStartTime;
                    var target = $(touchStartTarget);

                    // 如果是快速点击（小于500ms）且目标是交互元素
                    if (touchDuration < 500) {
                        if (target.is('.login-button') || target.closest('.login-button').length) {
                            e.preventDefault();
                            $('.login-button').click();
                        } else if (target.is('.checkbox-wrapper') || target.closest('.checkbox-wrapper').length) {
                            e.preventDefault();
                            var checkbox = target.closest('.checkbox-wrapper').find('input[type="checkbox"]');
                            checkbox.prop('checked', !checkbox.prop('checked'));
                        } else if (target.is('.form-input')) {
                            // 输入框正常处理，不阻止
                            return true;
                        }
                    }
                });

                // 防止双击缩放，但不影响表单元素
                var lastTouchEnd = 0;
                $(document).on('touchend', function(e) {
                    var now = Date.now();
                    var target = $(e.target);

                    // 只在非交互元素上防止双击
                    if (now - lastTouchEnd <= 300 &&
                        !target.is('input, button, label') &&
                        !target.closest('.login-card').length) {
                        e.preventDefault();
                    }
                    lastTouchEnd = now;
                });
            }
        </script>
    </form>
</body>
</html>
