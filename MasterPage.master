<%@ Master Language="C#" AutoEventWireup="true" CodeFile="MasterPage.master.cs" Inherits="MasterPage" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

    <title>
        <asp:ContentPlaceHolder ID="TitleContent" runat="server"><%=uConfig.stcdata("sitename") %></asp:ContentPlaceHolder>
    </title>
    <script src="js/jquery.min.js"></script>
    <link href="css/style.css" rel="stylesheet" />

    <link href="css/bootstrap.min.css" rel="stylesheet" />
    <script src="js/bootstrap.min.js"></script>
    <script src="js/bootstrap-paginator.min.js"></script>
    <script src="js/momo.js"></script>

    <style>
        /* 基础重置 */
        body, html, form, h1, h2, h3, h4, h5, h6, p {
            margin: 0px;
            padding: 0px;
        }

        body {
            background: #f8f9fa !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 响应式容器 */
        .icontainer {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        /* 头部导航样式 */
        .header-wrapper {
            background: #fff;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .header-content {
            display: flex;
            align-items: center;
            padding: 15px 0;
            min-height: 50px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            margin-right: 10px;
        }

        .site-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
            white-space: nowrap;
        }

        /* 桌面端导航菜单 */
        .desktop-menu {
            display: flex;
            margin-left: auto;
            gap: 20px;
        }

        .desktop-menu a {
            padding: 8px 16px;
            color: #666;
            text-decoration: none;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .desktop-menu a:hover {
            background: #f0f7ff;
            color: #1a73e8;
        }

        .desktop-menu a.active {
            background: #1a73e8;
            color: white;
        }

        /* 移动端汉堡菜单 */
        .mobile-menu-btn {
            display: none;
            margin-left: auto;
            background: none;
            border: none;
            padding: 8px;
            cursor: pointer;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .mobile-menu-btn:hover {
            background: #f5f5f5;
        }

        /* 响应式断点 */
        @media screen and (max-width: 768px) {
            .desktop-menu {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .site-title {
                font-size: 16px;
            }

            .logo-icon {
                width: 28px;
                height: 28px;
            }

            .header-content {
                padding: 12px 0;
            }
        }

        @media screen and (max-width: 480px) {
            .icontainer {
                padding: 0 10px;
            }
        }


        /* 用户信息区域 - 简约设计 */
        .user-info-section {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            margin-top: 80px;
        }

        .user-info-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            font-size: 14px;
        }

        .user-details {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-name {
            color: #333;
            font-weight: 500;
        }

        .balance-info {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 13px;
        }

        .balance-amount {
            color: #1a73e8;
            font-weight: 600;
            margin-left: 5px;
        }

        .logout-btn {
            background: #6c757d;
            color: white;
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .logout-btn:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }

        /* 公告区域 - 简约设计 */
        .notice-section {
            margin: 12px 0;
        }

        .notice-content {
            background: #fff;
            padding: 16px;
            border-radius: 6px;
            font-size: 13px;
            color: #555;
            line-height: 1.5;
            border: 1px solid #e9ecef;
        }

        /* 移动端优化 - 简约响应式 */
        @media screen and (max-width: 768px) {
            .user-info-section {
                margin-top: 70px;
            }

            .user-info-content {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
                padding: 12px 0;
            }

            .user-details {
                width: 100%;
                justify-content: space-between;
            }

            .logout-btn {
                align-self: flex-end;
                font-size: 11px;
                padding: 5px 10px;
            }

            .notice-content {
                padding: 12px;
                font-size: 12px;
            }
        }

        @media screen and (max-width: 480px) {
            .user-details {
                flex-direction: column;
                align-items: flex-start;
                gap: 6px;
            }

            .balance-info {
                font-size: 12px;
            }

            .user-info-content {
                padding: 10px 0;
            }
        }

        .table tr th,.table tr td {            
            white-space: nowrap;
        }
        
            .table-cell{
                position: sticky;
                left: 0;
                background:#fff;
            }

             .search_wrapper {
            background-color: #f5f5f5;
            border-radius: 20px;
            color: #999;
            height: 39px;
            padding: 0px 10px;
            display: flex;
            align-items: center;
        }


            .search_wrapper svg {
                margin-right: 5px;
            }


            .search_wrapper input {
                background: transparent;
                border: none;
                outline: none;
                color: #333;
                height: 39px;
                line-height: 39px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
                padding-right: 10px;
                font-size: 14px;
                font-weight: bold;
            }
    </style>

    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>
    <form id="form1" runat="server">

        <!-- 头部导航 -->
        <div class="header-wrapper">
            <div class="icontainer">
                <div class="header-content">
                    <div class="logo-section">
                        <h2 class="site-title"><%=uConfig.stcdata("sitename") %></h2>
                    </div>

                    <!-- 桌面端导航菜单 -->
                    <nav class="desktop-menu">
                        <a href="index.aspx" class="nav-link">提交订单</a>
                        <a href="serverorders.aspx" class="nav-link">我的订单</a>
                        <a href="transaction.aspx" class="nav-link">交易明细</a>
                    </nav>

                    <!-- 移动端汉堡菜单按钮 -->
                    <button class="mobile-menu-btn" onclick="openModal()" aria-label="打开菜单">
                        <svg width="24" height="24" viewBox="0 0 1024 1024" fill="currentColor">
                            <path d="M923.52 809.28V896h-832v-86.72h832zM247.04 454.4v86.656H91.52V454.4h155.52z m676.48 0v86.656H402.56V454.4h520.96zM620.288 128v86.72H91.52V128h528.768z m303.232 0v86.72h-209.92V128h209.92z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>




        <!-- 用户信息区域 -->
        <div class="user-info-section">
            <div class="icontainer">
                <div class="user-info-content">
                    <div class="user-details">
                        <span class="user-name"><%=uConfig.p_userNick %></span>
                        <div class="balance-info">
                            余额<span class="balance-amount"><%=Convert.ToDouble(uConfig.gd(dt,"score")).ToString("0.00") %></span>
                        </div>
                    </div>
                    <a href="out.aspx" class="logout-btn">退出</a>
                </div>
            </div>
        </div>

        <!-- 公告区域 -->
        <div class="icontainer">
            <div class="notice-section">
                <div class="notice-content">
                    <%=uConfig.stcdata("notify_business") %>
                </div>
            </div>
        </div>




        <div class="icontainer" style="padding-bottom: 100px;">
            <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
            </asp:ContentPlaceHolder>
        </div>


        <!--手机端菜单-->
        <%--<div class="bottom-menu">
            <a class="">
                <svg t="1690329754372" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2627" width="27" height="27">
                    <path d="M256 896a128 128 0 0 1-128-128v-321.408A128 128 0 0 1 177.408 345.6L407.210667 166.826667a170.666667 170.666667 0 0 1 209.578666 0l229.802667 178.773333A128 128 0 0 1 896 446.549333V768a128 128 0 0 1-128 128h-85.333333a85.333333 85.333333 0 0 1-85.333334-85.333333v-170.666667a42.666667 42.666667 0 0 0-42.666666-42.666667h-85.333334a42.666667 42.666667 0 0 0-42.624 40.405334L426.666667 637.653333h-0.042667L426.666667 640a42.666667 42.666667 0 1 1-85.290667-2.346667A128 128 0 0 1 469.333333 512h85.333334a128 128 0 0 1 128 128v128a42.666667 42.666667 0 0 0 42.666666 42.666667h42.666667a42.666667 42.666667 0 0 0 42.666667-42.666667v-321.408a42.666667 42.666667 0 0 0-16.469334-33.706667L564.394667 234.24a85.333333 85.333333 0 0 0-104.789334 0L229.802667 412.928a42.666667 42.666667 0 0 0-16.469334 33.706667V768a42.666667 42.666667 0 0 0 42.666667 42.666667h64a21.333333 21.333333 0 0 0 21.333333-21.333334 42.666667 42.666667 0 1 1 85.333334 0v21.333334a85.333333 85.333333 0 0 1-85.333334 85.333333H256z" fill="#14101C" p-id="2628"></path></svg>
                <div>首页</div>
            </a>
            <a class="">
                <svg t="1690330516672" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2213" width="27" height="27">
                    <path d="M262.792195 358.772176l498.414587 0c22.469758 0 40.676421-18.212804 40.676421-40.676421s-18.206664-40.676421-40.676421-40.676421L262.792195 277.419334c-22.463618 0-40.676421 18.212804-40.676421 40.676421S240.328577 358.772176 262.792195 358.772176z" fill="#1A1A1A" p-id="2214"></path><path d="M262.792195 738.403426l213.893508 0.013303c22.456455 0 40.676421-18.212804 40.676421-40.676421s-18.206664-40.676421-40.676421-40.676421l-213.893508-0.013303c-22.463618 0-40.676421 18.212804-40.676421 40.676421C222.115774 720.190623 240.322437 738.403426 262.792195 738.403426z" fill="#1A1A1A" p-id="2215"></path><path d="M262.792195 548.594453l498.414587 0c22.469758 0 40.676421-18.212804 40.676421-40.676421s-18.206664-40.676421-40.676421-40.676421L262.792195 467.24161c-22.463618 0-40.676421 18.212804-40.676421 40.676421S240.328577 548.594453 262.792195 548.594453z" fill="#1A1A1A" p-id="2216"></path><path d="M764.927523 78.118172 259.071454 78.118172c-107.251699 0-194.509019 87.25732-194.509019 194.509019l0 478.719013c0 107.265002 87.25732 194.535625 194.509019 194.535625l215.615733 0c22.469758 0 40.676421-18.206664 40.676421-40.676421 0-22.469758-18.206664-40.676421-40.676421-40.676421L259.071454 864.528985c-62.398142 0-113.157199-50.7785-113.157199-113.183805L145.914254 272.627191c0-62.398142 50.759057-113.157199 113.157199-113.157199l505.85607 0c62.390979 0 113.157199 50.759057 113.157199 113.157199l0 340.053859-79.525218 0c-97.188506 0-176.263469 79.074963-176.263469 176.269609l0 111.661126c0 9.688663 3.52529 18.464537 9.178033 25.449625 7.113001 11.828396 19.944238 19.820418 34.755572 19.820418l44.462654 0c33.208333 0 83.629699-34.029024 149.847843-101.160981 65.621557-66.508763 98.896404-116.175953 98.896404-147.616012l0-424.477644C959.437565 165.375491 872.179222 78.118172 764.927523 78.118172zM708.243523 864.530009l-4.594645 0 0-75.579349c0-52.334949 42.582841-94.91779 94.910627-94.91779l79.468936 0C864.123756 731.62607 743.378741 852.994278 708.243523 864.530009z" fill="#1A1A1A" p-id="2217"></path></svg>

                <div>订单</div>

            </a>
            <a class="">
                <svg t="1690330445570" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1605" width="27" height="27">
                    <path d="M772.725116 65.804728 251.302513 65.804728c-102.279455 0-185.494715 83.21526-185.494715 185.494715l0 521.401114c0 102.286618 83.21526 185.494715 185.494715 185.494715l521.422603 0c102.272291 0 185.465039-83.208097 185.465039-185.494715L958.190155 251.299443C958.191178 149.019988 874.997408 65.804728 772.725116 65.804728zM869.048929 772.700557c0 53.131081-43.207058 96.352465-96.323812 96.352465L251.302513 869.053022c-53.131081 0-96.352465-43.221384-96.352465-96.352465L154.950048 251.299443c0-53.131081 43.221384-96.352465 96.352465-96.352465l521.422603 0c53.116755 0 96.323812 43.221384 96.323812 96.352465L869.048929 772.700557z" fill="#1A1A1A" p-id="1606"></path><path d="M649.40137 525.311167c24.62177 0 44.571125-19.956517 44.571125-44.571125 0-24.613584-19.949354-44.571125-44.571125-44.571125L581.170382 436.168917l79.141478-79.152734c17.410532-17.403369 17.410532-45.630247 0-63.033615-17.410532-17.396205-45.644573-17.403369-63.026452 0.007163L512.017396 379.271046l-85.296665-85.279269c-17.410532-17.396205-45.644573-17.403369-63.026452 0.007163-17.410532 17.410532-17.410532 45.630247 0 63.033615l79.154781 79.138408-68.228941 0c-24.62177 0-44.571125 19.956517-44.571125 44.571125 0 24.614607 19.949354 44.571125 44.571125 44.571125l92.827175 0 0 39.057551-92.827175 0c-24.62177 0-44.571125 19.956517-44.571125 44.571125 0 24.613584 19.949354 44.571125 44.571125 44.571125l92.827175 0 0 62.213947c0 24.613584 19.949354 44.571125 44.571125 44.571125 24.62177 0 44.571125-19.956517 44.571125-44.571125l0-62.213947 92.812849 0c24.62177 0 44.571125-19.956517 44.571125-44.571125 0-24.614607-19.949354-44.571125-44.571125-44.571125l-92.812849 0 0-39.057551L649.40137 525.313214z" fill="#1A1A1A" p-id="1607"></path></svg>

                <div>流水</div>

            </a>
        </div>--%>



        <style>
            /* 移动端模态框样式 */
            .overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.6);
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 2000;
                backdrop-filter: blur(4px);
                -webkit-backdrop-filter: blur(4px);
            }

            .modal-container {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, 20px);
                padding: 30px 20px;
                opacity: 0;
                transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            }

            .modal-container.open {
                opacity: 1;
                transform: translate(-50%, -50%);
            }

            .modal-button {
                background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
                color: #333 !important;
                width: 280px;
                text-align: center;
                padding: 16px 20px;
                font-size: 16px;
                border-radius: 16px;
                font-weight: 500;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 15px;
                text-decoration: none !important;
                outline: none;
                transition: all 0.3s ease;
                border: 2px solid transparent;
                position: relative;
                overflow: hidden;
            }

            .modal-button::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(26, 115, 232, 0.1), transparent);
                transition: left 0.5s ease;
            }

            .modal-button:hover::before {
                left: 100%;
            }

            .modal-button:hover {
                background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
                color: white !important;
                transform: translateY(-2px);
                box-shadow: 0 12px 35px rgba(26, 115, 232, 0.3);
                border-color: #1a73e8;
            }

            .modal-button svg {
                margin-right: 12px;
                transition: transform 0.3s ease;
            }

            .modal-button:hover svg {
                transform: scale(1.1);
            }

            /* 移动端优化 */
            @media screen and (max-width: 480px) {
                .modal-button {
                    width: 260px;
                    padding: 14px 18px;
                    font-size: 15px;
                }

                .modal-container {
                    padding: 20px 15px;
                }
            }
        </style>
        <!-- 移动端导航模态框 -->
        <div class="overlay" id="overlay" onclick="closeModal()">
            <div class="modal-container" onclick="event.stopPropagation();">
                <a class="modal-button" href="index.aspx">
                    <svg width="24" height="24" viewBox="0 0 1024 1024" fill="currentColor">
                        <path d="M717.7216 860.1088H392.96c-77.5168 0-140.544-63.0272-140.544-140.544V478.1568c0-39.2192 12.1344-76.6976 35.072-108.4928 0.8192-1.1264 1.6896-2.1504 2.6112-3.1744l140.0832-147.6608c17.152-22.784 42.1376-37.4272 70.5024-41.3696 29.0816-3.9936 57.8048 3.8912 80.7936 22.1696 45.312 36.0448 53.6576 100.6592 18.9952 147.0464l-28.416 37.9904 178.944-11.0592c43.5712-2.6624 84.992 14.4896 113.8688 47.104 28.8768 32.6144 40.9088 75.9296 32.9728 118.784-0.0512 0.2048-0.0512 0.3584-0.1024 0.512l-41.8304 205.4144c-12.4928 66.4576-70.5536 114.688-138.1888 114.688z"></path>
                    </svg>
                    提交订单
                </a>

                <a class="modal-button" href="serverorders.aspx">
                    <svg width="24" height="24" viewBox="0 0 1024 1024" fill="currentColor">
                        <path d="M576.256 863.4368H309.0432c-73.4208 0-133.12-59.6992-133.12-133.12V392.4992c0-119.0912 96.9216-216.0128 216.0128-216.0128h267.2128c73.4208 0 133.12 59.6992 133.12 133.12v337.8176c0 119.0912-96.8704 216.0128-216.0128 216.0128z m-184.32-625.5104c-85.248 0-154.5728 69.3248-154.5728 154.5728v337.8176c0 39.5264 32.1536 71.68 71.68 71.68h267.2128c85.248 0 154.5728-69.3248 154.5728-154.5728V309.6064c0-39.5264-32.1536-71.68-71.68-71.68H391.936z"></path>
                    </svg>
                    我的订单
                </a>

                <a class="modal-button" href="transaction.aspx">
                    <svg width="24" height="24" viewBox="0 0 1024 1024" fill="currentColor">
                        <path d="M732.4672 839.3216H312.832c-85.4528 0-154.9824-74.8544-154.9824-166.912v-57.088c0-16.9472 13.7728-30.72 30.72-30.72 29.184 0 52.8896-27.2896 52.8896-60.8768 0-33.5872-23.7056-60.8768-52.8896-60.8768-16.9472 0-30.72-13.7728-30.72-30.72V375.0912c0-92.0064 69.5296-166.912 154.9824-166.912h419.6352c85.4528 0 154.9824 74.8544 154.9824 166.912V432.128c0 16.9472-13.7728 30.72-30.72 30.72-29.184 0-52.8896 27.2896-52.8896 60.8768 0 33.5872 23.7056 60.8768 52.8896 60.8768 16.9472 0 30.72 13.7728 30.72 30.72v57.088c0 92.0576-69.5296 166.************ 166.912z"></path>
                    </svg>
                    交易明细
                </a>
            </div>
        </div>

        <script>
            // 移动端导航模态框控制
            function openModal() {
                var overlay = document.getElementById('overlay');
                var modal = document.querySelector('.modal-container');

                // 防止页面滚动
                document.body.style.overflow = 'hidden';

                overlay.style.display = 'block';

                // 使用requestAnimationFrame确保动画流畅
                requestAnimationFrame(function() {
                    overlay.style.opacity = 1;
                    modal.classList.add('open');
                });
            }

            function closeModal() {
                var overlay = document.getElementById('overlay');
                var modal = document.querySelector('.modal-container');

                // 恢复页面滚动
                document.body.style.overflow = '';

                modal.classList.remove('open');
                overlay.style.opacity = 0;

                setTimeout(function () {
                    overlay.style.display = 'none';
                }, 300);
            }

            // 页面加载完成后的初始化
            $(document).ready(function() {
                // 高亮当前页面的导航链接
                var currentPage = window.location.pathname.split('/').pop();
                $('.desktop-menu .nav-link, .modal-button').each(function() {
                    var href = $(this).attr('href');
                    if (href === currentPage) {
                        $(this).addClass('active');
                    }
                });

                // 移动端触摸优化
                var lastTouchEnd = 0;
                $(document).on('touchend', function (event) {
                    var now = (new Date()).getTime();
                    if (now - lastTouchEnd <= 300) {
                        event.preventDefault();
                    }
                    lastTouchEnd = now;
                }, false);

                // ESC键关闭模态框
                $(document).keydown(function(e) {
                    if (e.keyCode === 27) { // ESC key
                        closeModal();
                    }
                });
            });
        </script>

        <style>
            /* 额外的移动端优化 */
            @media screen and (max-width: 768px) {
                /* 防止iOS Safari底部工具栏遮挡 */
                .icontainer:last-child {
                    padding-bottom: env(safe-area-inset-bottom, 20px);
                }

                /* 优化触摸目标大小 */
                .mobile-menu-btn {
                    min-width: 44px;
                    min-height: 44px;
                }
            }

            /* 滚动条样式优化 */
            ::-webkit-scrollbar {
                width: 6px;
            }

            ::-webkit-scrollbar-track {
                background: #f1f1f1;
            }

            ::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
            }

            ::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }

            /* 改善可访问性 */
            @media (prefers-reduced-motion: reduce) {
                * {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }
        </style>

    </form>
</body>
</html>
