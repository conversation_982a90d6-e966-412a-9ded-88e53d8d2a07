<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="serverorders.aspx.cs" Inherits="serverorders" %>

<asp:Content ID="Content1" ContentPlaceHolderID="TitleContent" runat="Server">
    我的订单 - <%=uConfig.stcdata("sitename") %>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="head" runat="Server">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* 响应式容器 */
        .orders-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 15px;
        }

        /* 搜索区域 */
        .search-section {
            background: #fff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #e9ecef;
        }

        .search-wrapper {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 6px;
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            margin-bottom: 12px;
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            outline: none;
            font-size: 14px;
            color: #333;
            margin: 0 8px;
        }

        .search-btn {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 6px 16px;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .search-btn:hover {
            background: #1557b0;
        }

        .filter-option {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #666;
        }

        .filter-option input[type="checkbox"] {
            margin-right: 8px;
        }

        /* 订单表格区域 */
        .orders-section {
            background: #fff;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .orders-table-wrapper {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .orders-table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 500;
            padding: 12px 8px;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
            white-space: nowrap;
        }

        .orders-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: middle;
        }

        .orders-table tr:hover {
            background: #f8f9fa;
        }

        /* 文本截断 */
        .text-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }

        /* 操作按钮 */
        .action-btn {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            border: none;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #1a73e8;
            color: white;
        }

        .btn-primary:hover {
            background: #1557b0;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        /* 自定义分页样式 */
        .pagination-section {
            padding: 20px 16px;
            text-align: center;
            background: #fff;
            border-radius: 8px;
            margin-top: 16px;
            border: 1px solid #e9ecef;
        }

        .custom-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .pagination-info {
            color: #666;
            font-size: 13px;
            margin-bottom: 12px;
        }

        .page-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
            padding: 0 8px;
            border: 1px solid #e9ecef;
            background: #fff;
            color: #495057;
            text-decoration: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }

        .page-btn:hover:not(.disabled):not(.active) {
            background: #f8f9fa;
            border-color: #dee2e6;
            color: #495057;
            text-decoration: none;
        }

        .page-btn.active {
            background: #1a73e8;
            border-color: #1a73e8;
            color: white;
        }

        .page-btn.disabled {
            background: #f8f9fa;
            border-color: #e9ecef;
            color: #adb5bd;
            cursor: not-allowed;
        }

        .page-btn.nav-btn {
            padding: 0 12px;
        }

        .page-input-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 12px;
        }

        .page-input {
            width: 50px;
            height: 32px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            text-align: center;
            font-size: 13px;
            outline: none;
        }

        .page-input:focus {
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
        }

        .go-btn {
            height: 32px;
            padding: 0 12px;
            border: 1px solid #1a73e8;
            background: #1a73e8;
            color: white;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .go-btn:hover {
            background: #1557b0;
        }

        /* 移动端分页优化 */
        @media (max-width: 768px) {
            .pagination-section {
                padding: 16px 12px;
            }

            .custom-pagination {
                gap: 4px;
            }

            .page-btn {
                min-width: 32px;
                height: 32px;
                font-size: 12px;
            }

            .page-btn.nav-btn {
                padding: 0 8px;
            }

            .page-input-group {
                margin: 8px 0;
                flex-basis: 100%;
                justify-content: center;
            }

            .pagination-info {
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .page-btn {
                min-width: 28px;
                height: 28px;
                font-size: 11px;
            }

            .page-input {
                width: 40px;
                height: 28px;
                font-size: 12px;
            }

            .go-btn {
                height: 28px;
                padding: 0 8px;
                font-size: 11px;
            }
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .orders-container {
                padding: 10px;
            }

            .search-section {
                padding: 12px;
            }

            .search-wrapper {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                margin: 8px 0;
                padding: 8px 0;
            }

            .search-btn {
                margin-top: 8px;
                padding: 10px;
            }

            .orders-table {
                font-size: 12px;
            }

            .orders-table th,
            .orders-table td {
                padding: 8px 4px;
            }

            .text-ellipsis {
                max-width: 100px;
            }
        }

        @media (max-width: 480px) {
            .orders-table {
                font-size: 11px;
            }

            .text-ellipsis {
                max-width: 80px;
            }

            .action-btn {
                padding: 3px 6px;
                font-size: 10px;
            }
        }
    </style>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="orders-container">


        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-wrapper">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="#999">
                    <g fill-rule="evenodd" clip-rule="evenodd">
                        <path d="M11.5 18.389c3.875 0 7-3.118 7-6.945 0-3.826-3.125-6.944-7-6.944s-7 3.118-7 6.944 3.125 6.945 7 6.945Zm0 1.5c4.694 0 8.5-3.78 8.5-8.445C20 6.781 16.194 3 11.5 3S3 6.78 3 11.444c0 4.664 3.806 8.445 8.5 8.445Z"></path>
                        <path d="M16.47 16.97a.75.75 0 0 1 1.06 0l3.5 3.5a.75.75 0 1 1-1.06 1.06l-3.5-3.5a.75.75 0 0 1 0-1.06Z"></path>
                    </g>
                </svg>
                <input class="search-input" placeholder="搜索订单ID、链接或备注..." id="search_text">
                <button class="search-btn" onclick="touch()">搜索</button>
            </div>
            <div class="filter-option">
                <input type="checkbox" class="record_orders" id="record_orders">
                <label for="record_orders">显示7天前的订单</label>
            </div>
        </div>

        <!-- 订单表格区域 -->
        <div class="orders-section">
            <div class="orders-table-wrapper">
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>类型</th>
                            <th>链接</th>
                            <th>备注</th>
                            <th>状态</th>
                            <th>点数</th>
                            <th>数量</th>
                            <th>初始量</th>
                            <th>当前量</th>
                            <th>订单状态</th>
                            <th>时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="list">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-section">
            <div class="pagination-info" id="paginationInfo"></div>
            <div class="custom-pagination" id="customPagination"></div>
        </div>


        </div>
    </div>

    <script>
        function checkIsSelect(obj) {
            if (obj.prop('checked')) {
                return true;
            } else {
                return false;
            }
        }

        function request(paras, url) {
            var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
            var paraObj = {}
            for (i = 0; j = paraString[i]; i++) {
                paraObj[j.substring(0, j.indexOf("=")).toLowerCase()] = j.substring(j.indexOf("=") + 1, j.length);
            }
            var returnValue = paraObj[paras.toLowerCase()];
            if (typeof (returnValue) == "undefined") {
                return "";
            } else {
                return returnValue;
            }
        }


        var clearTableData = function () {
            $('#list').html('');
        }
        var pushTableData = function (data) {
            var tbody = $('#list');
            var tr = $('<tr></tr>');
            tbody.append(tr);

            // 订单ID
            tr.append("<td><strong>" + data.taskid + "</strong></td>");

            // 订单类型
            tr.append("<td class='text-ellipsis' title='" + data.type + "'>" + data.type + (data.otherinfo != "" ? '<br><small>（' + data.otherinfo + '）</small>' : "") + "</td>");

            // 订单链接
            var linkHtml = "<div class='text-ellipsis' title='" + data.taskurl + "'>" + cJumpUrl(data.taskurl);
            if (data.order_url && data.order_url !== data.taskurl) {
                linkHtml += "<br><small style='color:#999;'>" + cJumpUrl(data.order_url, '#999') + "</small>";
            }
            linkHtml += "</div>";
            tr.append("<td>" + linkHtml + "</td>");

            // 订单备注
            tr.append("<td class='text-ellipsis' title='" + data.param + "'>" + (data.param || '-') + "</td>");

            // 操作状态
            var statusClass = getStatusClass(data.order_status);
            tr.append("<td><span class='status-badge " + statusClass + "'>" + data.order_status + "</span></td>");

            // 订单点数
            tr.append("<td><strong>" + data.total_fee + "</strong></td>");

            // 订单数量
            tr.append("<td>" + data.order_count + "</td>");

            // 初始量
            tr.append("<td>" + (data.order_start || '-') + "</td>");

            // 当前量
            tr.append("<td>" + (data.order_current || '-') + "</td>");

            // 关闭订单状态
            tr.append("<td>" + (data.state_text || '-') + "</td>");

            // 下单时间
            tr.append("<td><small>" + data.time + "</small></td>");

            // 操作按钮
            var actionHtml = '<div>';
            if (data.allow_refund == 1 && data.close_not_refund == 1 && data.state_text == "") {
                actionHtml += '<button class="action-btn btn-danger btn-close" data-id="' + data.taskid + '">关闭</button>';
            }
            if (data.refund_order == 1) {
                actionHtml += '<button class="action-btn btn-primary btn-refund" data-id="' + data.taskid + '">取消</button>';
            }
            actionHtml += '</div>';
            tr.append('<td>' + actionHtml + '</td>');

            // 代理商结算信息
            if (request('agent', location.href) == "1") {
                var settlementHtml = data.market_status == "1"
                    ? "<span class='status-badge status-success'>已结算 " + data.market_money + "</span>"
                    : "<span class='status-badge status-warning'>未结算 " + data.market_money + "</span>";
                tr.append("<td>" + settlementHtml + "</td>");
            }
        }

        // 获取状态样式类
        function getStatusClass(status) {
            if (status.includes('完成') || status.includes('成功')) {
                return 'status-success';
            } else if (status.includes('进行') || status.includes('处理')) {
                return 'status-warning';
            } else if (status.includes('失败') || status.includes('错误')) {
                return 'status-danger';
            }
            return 'status-warning';
        }

        var touch_data = {
            data: {
                table: 'orders',
                num: 10,
                index: 0,
                agent: request('agent', location.href)
            }
        }
        var touch = function () {
            touch_data.data.search_text = $("#search_text").val();
            touch_data.data.record_orders = checkIsSelect($(".record_orders"));
            $.ajax({
                type: 'post',
                dataType: "json",
                url: '<%=unified.apiurl%>list',
                data: touch_data.data,
                success: function (result) {
                    if (result.pager < 0) {
                        result.pager = 100
                    }

                    // 使用自定义分页
                    renderCustomPagination(result.index + 1, result.pager, result.result_list ? result.result_list.length : 0);



                    clearTableData();

                    if (result.status == 0) {

                        for (var i = 0; i < result.result_list.length; i++) {

                            pushTableData(result.result_list[i]);

                        }

                    }

                    function operator(type, order_id) {
                        $.ajax({
                            type: 'post', dataType: "json", url: '<%=unified.apiurl%>operator', data: { type: type, order_id: order_id }, success: function (result) {
                                if (result.info) {
                                    alert(result.info);
                                } else if (result.msg) {
                                    alert(result.msg);
                                }
                                touch();
                            }
                        });
                    }

                    $('.btn-close').unbind('click').on('click', function () {
                        if (confirm("是否确认关闭订单？")) {
                            operator('close_order', $(this).data("id"));
                        }
                    })
                    $('.btn-refund').unbind('click').on('click', function () {
                        if (confirm("是否确认操作？")) {
                            operator('cancel_order', $(this).data("id"));
                        }
                    })

                    $('.btn-replace').unbind('click').on('click', function () {
                        if (confirm("是否确认操作？")) {
                            operator('replace_people', $(this).data("id"));
                        }
                    })

                    $('.btn-switch').unbind('click').on('click', function () {
                        if (confirm("是否确认操作？")) {
                            operator('switch_order', $(this).data("id"));
                        }
                    })

                    $('.btn-repair').unbind('click').on('click', function () {
                        if (confirm("是否确认操作？")) {
                            operator('repair_order', $(this).data("id"));
                        }
                    })
                }
            });
        }
        touch();

        // 自定义分页渲染函数
        function renderCustomPagination(currentPage, totalPages, currentCount) {
            var paginationInfo = $('#paginationInfo');
            var paginationContainer = $('#customPagination');
            var isMobile = $(window).width() <= 768;

            // 显示分页信息
            var startItem = (currentPage - 1) * touch_data.data.num + 1;
            var endItem = startItem + currentCount - 1;
            if (isMobile) {
                paginationInfo.html('第 ' + currentPage + ' / ' + totalPages + ' 页');
            } else {
                paginationInfo.html('显示第 ' + startItem + ' - ' + endItem + ' 项，共 ' + totalPages + ' 页');
            }

            // 清空分页容器
            paginationContainer.empty();

            if (totalPages <= 1) {
                return; // 只有一页或没有数据时不显示分页
            }

            if (isMobile) {
                renderMobilePagination(currentPage, totalPages, paginationContainer);
            } else {
                renderDesktopPagination(currentPage, totalPages, paginationContainer);
            }
        }

        // 移动端分页渲染
        function renderMobilePagination(currentPage, totalPages, container) {
            // 上一页按钮
            var prevBtn = $('<a class="page-btn nav-btn ' + (currentPage === 1 ? 'disabled' : '') + '">‹</a>');
            if (currentPage > 1) {
                prevBtn.click(function() { goToPage(currentPage - 1); });
            }
            container.append(prevBtn);

            // 当前页码显示
            container.append('<span class="page-btn active">' + currentPage + '</span>');

            // 下一页按钮
            var nextBtn = $('<a class="page-btn nav-btn ' + (currentPage === totalPages ? 'disabled' : '') + '">›</a>');
            if (currentPage < totalPages) {
                nextBtn.click(function() { goToPage(currentPage + 1); });
            }
            container.append(nextBtn);

            // 跳转输入框（移动端简化版）
            var jumpGroup = $('<div class="page-input-group"></div>');
            var pageInput = $('<input type="number" class="page-input" min="1" max="' + totalPages + '" placeholder="页码">');
            var goBtn = $('<button class="go-btn">跳转</button>');

            goBtn.click(function() {
                var page = parseInt(pageInput.val());
                if (page >= 1 && page <= totalPages) {
                    goToPage(page);
                    pageInput.val('');
                }
            });

            pageInput.keypress(function(e) {
                if (e.which === 13) { // Enter键
                    goBtn.click();
                }
            });

            jumpGroup.append(pageInput);
            jumpGroup.append(goBtn);
            container.append(jumpGroup);
        }

        // 桌面端分页渲染
        function renderDesktopPagination(currentPage, totalPages, container) {
            // 首页按钮
            var firstBtn = $('<a class="page-btn nav-btn ' + (currentPage === 1 ? 'disabled' : '') + '">首页</a>');
            if (currentPage > 1) {
                firstBtn.click(function() { goToPage(1); });
            }
            container.append(firstBtn);

            // 上一页按钮
            var prevBtn = $('<a class="page-btn nav-btn ' + (currentPage === 1 ? 'disabled' : '') + '">上一页</a>');
            if (currentPage > 1) {
                prevBtn.click(function() { goToPage(currentPage - 1); });
            }
            container.append(prevBtn);

            // 页码按钮
            var startPage = Math.max(1, currentPage - 2);
            var endPage = Math.min(totalPages, currentPage + 2);

            // 如果开始页码大于1，显示省略号
            if (startPage > 1) {
                container.append('<a class="page-btn" onclick="goToPage(1)">1</a>');
                if (startPage > 2) {
                    container.append('<span class="page-btn disabled">...</span>');
                }
            }

            // 显示页码
            for (var i = startPage; i <= endPage; i++) {
                var pageBtn = $('<a class="page-btn ' + (i === currentPage ? 'active' : '') + '">' + i + '</a>');
                if (i !== currentPage) {
                    pageBtn.attr('onclick', 'goToPage(' + i + ')');
                }
                container.append(pageBtn);
            }

            // 如果结束页码小于总页数，显示省略号
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    container.append('<span class="page-btn disabled">...</span>');
                }
                container.append('<a class="page-btn" onclick="goToPage(' + totalPages + ')">' + totalPages + '</a>');
            }

            // 下一页按钮
            var nextBtn = $('<a class="page-btn nav-btn ' + (currentPage === totalPages ? 'disabled' : '') + '">下一页</a>');
            if (currentPage < totalPages) {
                nextBtn.click(function() { goToPage(currentPage + 1); });
            }
            container.append(nextBtn);

            // 末页按钮
            var lastBtn = $('<a class="page-btn nav-btn ' + (currentPage === totalPages ? 'disabled' : '') + '">末页</a>');
            if (currentPage < totalPages) {
                lastBtn.click(function() { goToPage(totalPages); });
            }
            container.append(lastBtn);

            // 跳转输入框
            var jumpGroup = $('<div class="page-input-group"></div>');
            jumpGroup.append('<span style="font-size: 12px; color: #666;">跳转到</span>');
            var pageInput = $('<input type="number" class="page-input" min="1" max="' + totalPages + '" placeholder="' + currentPage + '">');
            var goBtn = $('<button class="go-btn">GO</button>');

            goBtn.click(function() {
                var page = parseInt(pageInput.val());
                if (page >= 1 && page <= totalPages) {
                    goToPage(page);
                }
            });

            pageInput.keypress(function(e) {
                if (e.which === 13) { // Enter键
                    goBtn.click();
                }
            });

            jumpGroup.append(pageInput);
            jumpGroup.append(goBtn);
            container.append(jumpGroup);
        }

        // 跳转到指定页面
        function goToPage(page) {
            touch_data.data.index = page - 1;
            touch();
        }

        // 窗口大小变化时重新渲染分页
        $(window).resize(function() {
            var paginationContainer = $('#customPagination');
            if (paginationContainer.children().length > 0) {
                // 如果分页已经渲染，重新渲染以适应新的屏幕尺寸
                var currentPage = parseInt($('.page-btn.active').text()) || 1;
                var totalPages = parseInt($('.page-input').attr('max')) || 1;
                var currentCount = $('#list tr').length;
                renderCustomPagination(currentPage, totalPages, currentCount);
            }
        });
    </script>

    <script>
        var cJumpUrl = function (url, color) {
            color = color || '#1a73e8';
            return url.indexOf("http") != -1 ? "<a target='_blank' href='" + url + "' style='color:" + color + "; text-decoration: none;'>" + url + "</a>" : url;
        }
    </script>
</asp:Content>

