<%@ Master Language="C#" AutoEventWireup="true" CodeFile="m.master.cs" Inherits="global_m" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

    <link href="../static/mgr/css/font-awesome.min.css" rel="stylesheet" />
    <link href="../static/mgr/layui/css/layui.css" rel="stylesheet" />
    <link href="../static/mgr/css/global.css" rel="stylesheet" />
    <link href="../static/mgr/css/css.css" rel="stylesheet" />

    <script src="../static/mgr/js/jquery_1.9.1_jquery.min.js"></script>
    <script src="../static/mgr/js/modernizr.js"></script>
    <script src="../static/mgr/layer/layer.js"></script>
    <script src="../static/mgr/layui/layui.js"></script>
    <script src="../static/mgr/js/global.js"></script>
    <script src="../static/mgr/js/fuzhu.js"></script>

    <style>
        /* 基础重置和全局样式 */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa;
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        a {
            outline: 0 !important;
            outline: none !important;
            text-decoration: none;
        }

        /* 现代化顶部导航栏 */
        .modern-navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: #fff;
            border-bottom: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .navbar-brand:hover {
            color: #1a73e8;
        }

        .navbar-brand img {
            width: 32px;
            height: 32px;
            margin-right: 12px;
            border-radius: 6px;
        }

        .navbar-menu {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .navbar-link {
            color: #666;
            font-size: 14px;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .navbar-link:hover {
            background: #f8f9fa;
            color: #1a73e8;
        }

        .user-dropdown {
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .user-dropdown:hover {
            background: #f8f9fa;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            margin-right: 8px;
        }

        .user-name {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .dropdown-arrow {
            margin-left: 8px;
            color: #999;
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .user-dropdown.open .dropdown-arrow {
            transform: rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            min-width: 150px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1001;
        }

        .user-dropdown.open .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: block;
            padding: 12px 16px;
            color: #666;
            font-size: 14px;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.3s ease;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: #f8f9fa;
            color: #1a73e8;
        }

        /* 移动端汉堡菜单 */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            padding: 8px;
            cursor: pointer;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .mobile-menu-btn:hover {
            background: #f8f9fa;
        }

        .hamburger {
            width: 24px;
            height: 18px;
            position: relative;
        }

        .hamburger span {
            display: block;
            position: absolute;
            height: 2px;
            width: 100%;
            background: #333;
            border-radius: 1px;
            opacity: 1;
            left: 0;
            transform: rotate(0deg);
            transition: 0.25s ease-in-out;
        }

        .hamburger span:nth-child(1) {
            top: 0px;
        }

        .hamburger span:nth-child(2) {
            top: 8px;
        }

        .hamburger span:nth-child(3) {
            top: 16px;
        }

        .hamburger.open span:nth-child(1) {
            top: 8px;
            transform: rotate(135deg);
        }

        .hamburger.open span:nth-child(2) {
            opacity: 0;
            left: -60px;
        }

        .hamburger.open span:nth-child(3) {
            top: 8px;
            transform: rotate(-135deg);
        }

        /* 现代化侧边栏 */
        .modern-sidebar {
            position: fixed;
            top: 60px;
            left: 0;
            width: 260px;
            height: calc(100vh - 60px);
            background: #fff;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            transition: transform 0.3s ease;
            z-index: 999;
        }

        .sidebar-nav {
            padding: 20px 0;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin: 0 12px 4px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: #666;
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: #f8f9fa;
            color: #1a73e8;
            text-decoration: none;
        }

        .nav-link.active {
            background: #e3f2fd;
            color: #1a73e8;
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: #1a73e8;
            border-radius: 0 2px 2px 0;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .nav-link:hover .nav-icon,
        .nav-link.active .nav-icon {
            opacity: 1;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 260px;
            margin-top: 60px;
            padding: 24px;
            min-height: calc(100vh - 60px);
            transition: margin-left 0.3s ease;
        }

        /* 响应式设计 */
        @media screen and (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            .navbar-menu .navbar-link {
                display: none;
            }

            .modern-sidebar {
                transform: translateX(-100%);
            }

            .modern-sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 998;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .sidebar-overlay.show {
                opacity: 1;
                visibility: visible;
            }
        }

        @media screen and (max-width: 480px) {
            .modern-navbar {
                padding: 0 15px;
            }

            .main-content {
                padding: 16px;
            }

            .navbar-brand {
                font-size: 16px;
            }

            .navbar-brand img {
                width: 28px;
                height: 28px;
            }
        }

        /* 表单优化 */
        .layui-form-label {
            width: 100px;
            font-weight: 500;
            color: #333;
        }

        .layui-form-item {
            margin-bottom: 16px;
        }

        .layui-input, .layui-select, .layui-textarea {
            border-radius: 6px;
            border: 1px solid #e9ecef;
            transition: border-color 0.3s ease;
        }

        .layui-input:focus, .layui-select:focus, .layui-textarea:focus {
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
        }

        /* 按钮优化 */
        .layui-btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .layui-btn-primary {
            background: #1a73e8;
            border-color: #1a73e8;
        }

        .layui-btn-primary:hover {
            background: #1557b0;
            border-color: #1557b0;
        }

        /* 表格优化 */
        .layui-table {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .layui-table th {
            background: #f8f9fa;
            font-weight: 500;
            color: #333;
        }

        .layui-table tr:hover {
            background: #f8f9fa;
        }

        /* 模态框优化 */
        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        .modal-header {
            border-bottom: 1px solid #e9ecef;
            padding: 20px 24px;
        }

        .modal-title {
            font-weight: 600;
            color: #333;
        }

        .modal-body {
            padding: 24px;
        }

        /* 加载条优化 */
        #page-loading-bar {
            position: fixed;
            top: 60px;
            left: 0;
            width: 100%;
            height: 3px;
            background: #f0f0f0;
            z-index: 1001;
        }

        #page-loading-bar dd {
            height: 100%;
            background: linear-gradient(90deg, #1a73e8, #4285f4);
            width: 0;
            transition: width 0.3s ease;
        }
    </style>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body>
    <form id="form1" runat="server">
        <!-- 现代化顶部导航栏 -->
        <nav class="modern-navbar">
            <div class="navbar-brand" onclick="toggleSidebar()">
                <img src="../static/images/logo.png" alt="Logo" />
                管理后台
            </div>

            <!-- 移动端汉堡菜单 -->
            <button class="mobile-menu-btn" onclick="toggleSidebar()">
                <div class="hamburger" id="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </button>

            <div class="navbar-menu">
                <%if (uConfig.p_userNickAD == "gm-facai") { %>
                    <a href="../index.aspx" target="_blank" class="navbar-link">前台首页</a>
                <%} %>

                <div class="user-dropdown" id="userDropdown">
                    <div class="user-avatar">
                        <span class="show_nick_initial"></span>
                    </div>
                    <span class="user-name show_nick"></span>
                    <span class="dropdown-arrow">▼</span>

                    <div class="dropdown-menu">
                        <a href="../mgr_data/out.aspx" class="dropdown-item">
                            <i class="fa fa-sign-out" style="margin-right: 8px;"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </nav>
        <div>
            <div id="page-loading-bar">
                <dl>
                    <dd></dd>
                </dl>
            </div>

        <!-- 现代化侧边栏 -->
        <aside class="modern-sidebar" id="sidebar">
            <ul class="sidebar-nav">
                <li class="nav-item">
                    <a href="../mgr_data/_index.aspx" class="nav-link">
                        <i class="nav-icon fa fa-dashboard"></i>
                        <span>数据统计</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="../mgr_data/app_group.aspx" class="nav-link">
                        <i class="nav-icon fa fa-tags"></i>
                        <span>分组管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="../mgr_data/bus.aspx" class="nav-link">
                        <i class="nav-icon fa fa-cube"></i>
                        <span>产品管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="../mgr_data/orders.aspx" class="nav-link">
                        <i class="nav-icon fa fa-list-alt"></i>
                        <span>订单管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="../mgr_data/recordslist.aspx" class="nav-link">
                        <i class="nav-icon fa fa-credit-card"></i>
                        <span>账单记录</span>
                    </a>
                </li>

                <%if (uConfig.p_userNickAD == "gm-facai") { %>
                <li class="nav-item">
                    <a href="../mgr_data/new_users.aspx" class="nav-link">
                        <i class="nav-icon fa fa-users"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="../mgr_data/system.aspx" class="nav-link">
                        <i class="nav-icon fa fa-shield"></i>
                        <span>系统安全</span>
                    </a>
                </li>
                <%} %>
            </ul>
        </aside>

        <!-- 移动端遮罩层 -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="closeSidebar()"></div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server"></asp:ContentPlaceHolder>
        </main>

            <div class="clearfix"></div>


        </div>

        <script src="../static/mgr/js/bootstrap.min.js"></script>

        <script src="../static/mgr/js/platform.js"></script>


        <script>var userNick2 = getCookie("nick_admin");; if (userNick2) { $(".show_nick").html(userNick2) };</script>

        <script>

            function login() {
                var nick = $("#nick").val();
                var pass = $("#pass").val();

                $.ajax({
                    type: "POST",
                    url: "../Api/user.aspx?action=login",
                    data: { nick: nick, pass: pass },
                    datatype: "html",
                    success: function (data) {
                        if (data.indexOf(".aspx") != -1) {
                            location.href = data;
                        } else {
                            alert(data);
                        }
                    },
                    error: function () {
                        alert(_expText);
                    }
                });
            }
        </script>


        <script>
            $(".sub-menu>li>a").click(function () {
                $(this).closest("li").toggleClass("open2");
            })
        </script>




        <div class="fade modal m2" id="tipModal2" tabindex="998" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true"></span><span class="sr-only">Close</span></button><h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <div class="txtarea-wrap tipData" style="color: #333;">
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div id="show-dialog" class="modal fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">系统提示</h4>
                    </div>
                    <div class="modal-body tipData">
                    </div>
                </div>
            </div>
        </div>


        <script>
            var tips2 = function (text, t) {
                t = typeof (t) == undefined ? "系统提示" : t;
                //$("#tipModal2 .modal-title").html(t);
                //$("#tipModal2 .tipData").html(text);
                //$('#tipModal2').modal();

                $("#show-dialog .modal-title").html(t);
                $("#show-dialog .tipData").html(text);
                $('#show-dialog').modal();
            }

            // 复制函数
            function copyText() {
                var Url2 = document.getElementById("cardData");
                Url2.select(); // 选择对象
                document.execCommand("Copy"); // 执行浏览器复制命令
            }

            function close_modal() {
                $('#show-dialog').modal('hide');
            }
        </script>


        <script>
            function toggleMenu() {
                var disp = $(".sidebar").css("display");
                if (disp == "none") {
                    location.href = "../index.aspx";
                    return;
                }
                var left = $(".sidebar").css("left");
                if (left == "0px") {
                    $(".sidebar").animate({ "left": "-190px" });
                    $(".manage-body").animate({ "margin-left": "0px" });
                } else {
                    $(".sidebar").animate({ "left": "0px" });
                    $(".manage-body").animate({ "margin-left": "190px" });
                }
            }

            function logoClick() {
                toggleMenu();
            }

            function event_create(arr) {
                var tiptext = "是否确认此次操作？";
                if (arr['event'] == "delete") {
                    tiptext = "是否确认删除此条数据？";
                }

                layer.confirm(tiptext, {
                    btn: ['确认', '取消']
                }, function () {
                    var mark = mask = layer.load(2, { shade: [0.8, '#393D49'] });;
                    $.ajax({
                        type: "POST",
                        url: "../mgr_data/apidata.aspx?do=event_create",
                        data: arr,
                        dataType: "json",
                        success: function (data) {
                            layer.close(mask);
                            if (data.code == 1) {
                                if (typeof (getPager) == "function") {
                                    getPager();
                                } else {
                                    location.href = location.href;
                                }
                                layer.msg(data.msg, { icon: 1, time: 1200 });
                            } else {
                                layer.msg(data.msg, { icon: 2 });
                            }
                        },
                        error: function (data) {
                            layer.close(mask);
                            console.log("出现错误：", data);
                        }
                    });
                }, function () {
                });
            }

            var open_new_page = function (u, t) {
                location.href = u;
            }

            // 现代化UI初始化
            $(function () {
                // 显示用户昵称
                var userNick = "<%=uConfig.p_userNickAD %>";
                $(".show_nick").text(userNick);
                $(".show_nick_initial").text(userNick.charAt(0).toUpperCase());

                // 高亮当前页面菜单项
                highlightCurrentMenu();

                // 初始化用户下拉菜单
                initUserDropdown();

                // 移动端触摸优化
                initMobileOptimization();
            });

            // 侧边栏切换
            function toggleSidebar() {
                var sidebar = document.getElementById('sidebar');
                var overlay = document.getElementById('sidebarOverlay');
                var hamburger = document.getElementById('hamburger');

                if (window.innerWidth <= 768) {
                    // 移动端
                    sidebar.classList.toggle('open');
                    overlay.classList.toggle('show');
                    hamburger.classList.toggle('open');

                    // 防止背景滚动
                    if (sidebar.classList.contains('open')) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow = '';
                    }
                }
            }

            // 关闭侧边栏
            function closeSidebar() {
                var sidebar = document.getElementById('sidebar');
                var overlay = document.getElementById('sidebarOverlay');
                var hamburger = document.getElementById('hamburger');

                sidebar.classList.remove('open');
                overlay.classList.remove('show');
                hamburger.classList.remove('open');
                document.body.style.overflow = '';
            }

            // 高亮当前页面菜单项
            function highlightCurrentMenu() {
                var currentPath = window.location.pathname;
                var menuLinks = document.querySelectorAll('.nav-link');

                menuLinks.forEach(function(link) {
                    var href = link.getAttribute('href');
                    if (href && currentPath.indexOf(href.replace('../mgr_data/', '')) !== -1) {
                        link.classList.add('active');
                    }
                });
            }

            // 初始化用户下拉菜单
            function initUserDropdown() {
                var userDropdown = document.getElementById('userDropdown');

                userDropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                    this.classList.toggle('open');
                });

                // 点击其他地方关闭下拉菜单
                document.addEventListener('click', function() {
                    userDropdown.classList.remove('open');
                });
            }

            // 移动端优化
            function initMobileOptimization() {
                // 防止双击缩放
                var lastTouchEnd = 0;
                document.addEventListener('touchend', function (event) {
                    var now = (new Date()).getTime();
                    if (now - lastTouchEnd <= 300) {
                        event.preventDefault();
                    }
                    lastTouchEnd = now;
                }, false);

                // 窗口大小变化时处理侧边栏
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        closeSidebar();
                    }
                });
            }

            // ESC键关闭侧边栏
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeSidebar();
                }
            });
        </script>
    </form>
</body>
</html>
