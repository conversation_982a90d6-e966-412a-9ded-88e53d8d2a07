<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="index.aspx.cs" Inherits="index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="TitleContent" runat="Server">
    首页 - <%=uConfig.stcdata("sitename") %>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="head" runat="Server">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* 响应式容器 */
        .mobile-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 10px;
        }

        /* 平台选择区域 */
        .platform-section {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        /* 平台图标网格布局 */
        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 12px;
            justify-items: center;
        }

        @media (max-width: 768px) {
            .platform-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .platform-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 6px;
            }
        }
    </style>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="mobile-container">
        <!-- 平台选择区域 -->
        <div class="platform-section">
            <div class="section-title">选择平台</div>
            <div class="platform-grid classtype1">
                <a class="classtype_active" type="tk" title="TikTok">
                    <img src="../static/images/b/tiktok.JPG" alt="TikTok" />
                </a>
                <a type="ins" title="Instagram">
                    <img src="../static/images/b/INS.JPG" alt="Instagram" />
                </a>
                <a type="fb" title="Facebook">
                    <img src="../static/images/b/FB.JPG" alt="Facebook" />
                </a>
                <a type="ytb" title="YouTube">
                    <img src="../static/images/b/youtube.JPG" alt="YouTube" />
                </a>
                <a type="twitch" title="Twitch">
                    <img src="../static/images/b/twitch.png" alt="Twitch" />
                </a>
                <a type="shopee" title="Shopee">
                    <img src="../static/images/b/shopee.png" alt="Shopee" />
                </a>
                <a type="tw" title="Twitter">
                    <img src="../static/images/b/twitter.JPG" alt="Twitter" />
                </a>
                <%--<a type="tg" title="Telegram">
                    <img src="../static/images/b/telegram.PNG" alt="Telegram" />
                </a>--%>
                <a type="bigo" title="Bigo">
                    <img src="../static/images/b/bigo.png" alt="Bigo" />
                </a>
                <a type="steam" title="Steam">
                    <img src="../static/images/b/steam.png" alt="Steam" />
                </a>
                <a type="lazada" title="Lazada">
                    <img src="../static/images/b/lazada.png" alt="Lazada" />
                </a>
            </div>
        </div>

        <script id="order_business" type="text/ecmascript">
            <%=order_business %>
        </script>

        <!-- 业务类型选择区域 -->
        <div class="platform-section">
            <div class="section-title">业务类型</div>
            <div class="business-type-tabs classtype2">
                <a type="" class="classtype_active">全部</a>
                <%--
                <a type="like">点赞</a>
                <a type="comment">评论</a>--%>
                <a type="online">人气</a>
                <a type="fans">粉丝</a>
                <a type="view">播放</a>
            </div>
        </div>

        <style>
            /* 平台图标样式 */
            .classtype1 a {
                display: flex;
                align-items: center;
                justify-content: center;
                background: #fff;
                border: 2px solid #f0f0f0;
                border-radius: 12px;
                padding: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
                min-height: 60px;
                position: relative;
                overflow: hidden;
            }

            .classtype1 a img {
                height: 32px;
                width: auto;
                max-width: 100%;
                object-fit: contain;
            }

            .classtype1 a.classtype_active {
                border-color: #1a73e8;
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(26, 115, 232, 0.3);
            }

            .classtype1 a:hover:not(.classtype_active) {
                border-color: #1a73e8;
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }

            @media (max-width: 768px) {
                .classtype1 a {
                    min-height: 50px;
                    padding: 8px;
                }

                .classtype1 a img {
                    height: 24px;
                }
            }

            /* 业务类型标签样式 */
            .business-type-tabs {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                justify-content: center;
            }

            .classtype2 a {
                position: relative;
                display: inline-block;
                background: #f8f9fa;
                color: #6c757d;
                font-weight: 500;
                padding: 10px 20px;
                border-radius: 25px;
                cursor: pointer;
                text-decoration: none;
                transition: all 0.3s ease;
                border: 2px solid transparent;
                font-size: 14px;
            }

            .classtype2 a.classtype_active {
                background: #1a73e8;
                color: white;
                border-color: #1a73e8;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
            }

            .classtype2 a:hover:not(.classtype_active) {
                background: #e9ecef;
                color: #495057;
                transform: translateY(-1px);
            }

            @media (max-width: 768px) {
                .classtype2 a {
                    padding: 8px 16px;
                    font-size: 13px;
                }
            }
        </style>

        <!-- 订单表单区域 -->
        <div class="platform-section">
            <div class="section-title">订单信息</div>

            <!-- 业务选择 -->
            <div class="form-group">
                <label class="form-label">选择业务</label>
                <div class="select-wrapper">
                    <select id="orderTypes" class="form-select">
                        <asp:Repeater ID="items" runat="server">
                            <ItemTemplate>
                                <option value="<%#Eval("id") %>/<%#Eval("param") %>">★★★推荐★★★ | <%#Eval("appname") %></option>
                            </ItemTemplate>
                        </asp:Repeater>
                    </select>
                </div>
            </div>

            <!-- 链接输入 -->
            <div class="form-group">
                <label class="form-label">链接信息</label>
                <textarea id="orderUrls" class="form-textarea" placeholder="链接信息，批量下单格式（一行代表一个订单）：链接,数量" autocomplete="off"></textarea>
            </div>

            <!-- 隐藏字段 -->
            <div class="form-group hInput" style="display: none;">
                <label class="form-label">订单备注</label>
                <input id="orderNote" class="form-input" placeholder="订单备注" autocomplete="off">
            </div>

            <div class="form-group hInput" style="display: none;">
                <label class="form-label">评论内容</label>
                <textarea id="orderComments" class="form-textarea" placeholder="评论内容" autocomplete="off"></textarea>
            </div>

            <div class="form-group hInput" style="display: none;">
                <label class="form-label">直播时长</label>
                <input id="orderTime" class="form-input" placeholder="直播时长（小时）" autocomplete="off">
            </div>

            <!-- 订单数量 -->
            <div class="form-group">
                <label class="form-label">订单数量</label>
                <input id="orderNum" class="form-input" placeholder="订单数量" autocomplete="off">
            </div>

            <!-- 费用显示 -->
            <div class="form-group">
                <label class="form-label">费用计算</label>
                <input id="orderTotal" class="form-input total-display" disabled="disabled" value="" autocomplete="off">
            </div>

            <!-- 提交按钮 -->
            <div class="submit-section">
                <button class="submit-btn" id="orderTask_Btn">
                    <span class="btn-text">提交订单</span>
                    <span class="btn-loading" style="display: none;">处理中...</span>
                </button>
            </div>
        </div>

        <!-- 提示信息 -->
        <div class="info-section">
            <div id="OrderText" class="order-info"></div>
        </div>
    </div>

    <script>
        $('.classtype1 a').on('click', function () {
            $(this).addClass('classtype_active').siblings().removeClass('classtype_active');
            $(".classtype2 a").eq(0).addClass('classtype_active').siblings().removeClass('classtype_active');
            refresh_classtype();
        })

        $('.classtype2 a').on('click', function () {
            $(this).addClass('classtype_active').siblings().removeClass('classtype_active');
            refresh_classtype();
        })

        function refresh_classtype() {
            var obj = JSON.parse($('#order_business').html());
            $('#orderTypes').html('<option value="">请选择业务</option>');

            var type1 = $('.classtype1 a.classtype_active').attr("type");
            var type2 = $('.classtype2 a.classtype_active').attr("type");

            for (var i = 0; i < obj.length; i++) {
                if (obj[i].typename == type1 && (type2 == "" || type2 == obj[i].type2)) {
                    var pretext = '★★★推荐★★★ | ';
                    pretext = "";
                    $('#orderTypes').append('<option value="' + obj[i].id + '/' + obj[i].param + '">' + pretext + obj[i].appname + '</option>');
                }
            }

            $("#orderTypes").change();
        }

        refresh_classtype();
    </script>

    <style>
        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            color: #333;
            background: #fff;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        .select-wrapper {
            position: relative;
        }

        .select-wrapper::after {
            content: '▼';
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            pointer-events: none;
            font-size: 12px;
        }

        .total-display {
            background: #f8f9fa !important;
            color: #495057 !important;
            font-weight: 600;
        }

        /* 提交按钮区域 */
        .submit-section {
            text-align: center;
            margin-top: 30px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 40px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 200px;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover:not(.submit_status) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 115, 232, 0.3);
        }

        .submit-btn.submit_status {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 信息区域 */
        .info-section {
            text-align: center;
            margin-top: 20px;
        }

        .order-info {
            color: #6c757d;
            font-size: 13px;
            line-height: 1.5;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .mobile-container {
                padding: 5px;
            }

            .platform-section {
                padding: 15px;
                margin-bottom: 15px;
            }

            .section-title {
                font-size: 15px;
            }

            .form-input, .form-select, .form-textarea {
                padding: 10px 12px;
                font-size: 16px; /* 防止iOS缩放 */
            }

            .submit-btn {
                width: 100%;
                max-width: 300px;
                padding: 12px 30px;
                font-size: 15px;
            }
        }

        @media (max-width: 480px) {
            .platform-section {
                padding: 12px;
            }

            .form-textarea {
                min-height: 100px;
            }
        }
    </style>

    <script>
        var kData = {};
        var kTask = new Array();

        var r = function (type, data, callback) {
            $.ajax({
                type: 'post',
                dataType: "json",
                url: '?do=' + type,
                data: data,
                success: function (result) {
                    callback(result)
                }
            });
        }
        $("#orderTypes").on('change', function () {
            var typeid = $(this).val();
            if (typeid == null || typeid == "") {
                $('#orderTotal').val('请选择业务');
                return;
            }
            var param = typeid.split('/')[1];
            typeid = typeid.split('/')[0];

            $('#orderUrls').val('');
            $('#orderNum').val('');
            $('.hInput').hide();
            $('.hInput').find('.w_input').val('');

            //console.log('check_param', param);
            switch (param) {
                case "customTimes":
                    $('#orderTime').closest('.ccvoYb').show();
                    break;
                case "comment_list":
                    $('#orderNote').closest('.ccvoYb').show();
                    break;
                case "custom-comment":
                    $('#orderComments').closest('.ccvoYb').show();
                    break;
                default:
                    break;
            }
            r('json_info', { typeid: typeid }, function (result) {
                //console.log("result", result);
                if (result.code == 1) {
                    $('#OrderText').text(result.tixin);
                    kData.appid = typeid;
                    kData.dianshu = parseFloat(result.dianshu);
                    $('#orderTotal').val('单价' + kData.dianshu + '点，累计消费' + kData.dianshu + '点');
                }
            })
        })

        //去除数组重复
        function unique(arr) {
            var result = [], hash = {};
            for (var i = 0, elem; (elem = arr[i]) != null; i++) {
                if (!hash[elem]) {
                    result.push(elem);
                    hash[elem] = true;
                }
            }
            return result;
        }

        $('#orderComments').on('keyup', function () {
            var comment_data = $(this).val();
            var arr = comment_data.split('\n');
            arr = unique(arr);
            //console.log('comment_data', arr);
            var new_comment_data = arr.join('\n');
            $('#orderNum').val(arr.length);
            check_total_dianshu();
            if (new_comment_data != comment_data) {
                $('#orderComments').val(new_comment_data);
            }
        });

        $("#orderUrls").on('keyup', function () {
            check_total_dianshu();
        })

        $("#orderNum").on('keyup', function () {
            check_total_dianshu();
        })

        $("#orderTime").on('keyup', function () {
            check_total_dianshu();
        })

        $("#orderTask_Btn").on('click', function () {
            if ($(this).hasClass("submit_status")) {
                return;
            }

            // 添加加载状态
            $(this).addClass("submit_status");
            $(this).find('.btn-text').hide();
            $(this).find('.btn-loading').show();

            check_total_dianshu();
            pushTask();
        })

        var check_total_dianshu = function () {
            kTask = [];
            var taskNum = $("#orderNum").val();
            var taskID = $("#orderUrls").val();
            if (taskID.indexOf("    ") != -1 || taskID.indexOf("----") != -1 || taskID.indexOf(",") != -1 || taskID.indexOf("，") != -1) {
                var arr = taskID.split('\n');
                taskNum = 0;
                for (var i = 0; i < arr.length; i++) {
                    var arr2;
                    if (arr[i].indexOf("----") != -1) {
                        arr2 = arr[i].split("----");
                    } else if (arr[i].indexOf(",") != -1) {
                        arr2 = arr[i].split(",");
                    } else if (arr[i].indexOf("，") != -1) {
                        arr2 = arr[i].split("，");
                    } else {
                        arr2 = arr[i].split("    ");
                    }
                    if (arr2.length == 2) {
                        taskNum += parseInt(arr2[1]);

                        kTask.push({
                            id: arr2[0],
                            num: parseInt(arr2[1])
                        })
                    }
                }
            } else {
                if (taskNum == "" || isNaN(taskNum) || parseInt(taskNum) < 0) {
                    taskNum = "0";
                }

                kTask.push({
                    id: taskID,
                    num: parseInt(taskNum)
                })
            }
            taskInfo = new Array();

            var total_fee = (kData.dianshu * parseInt(taskNum)).toFixed(4);

            var customTimes = $('#orderTime').val();
            if (customTimes != "") {
                total_fee = parseFloat(total_fee) * parseInt(customTimes);
            }

            $('#orderTotal').val('单价' + kData.dianshu + '点，累计消费' + parseFloat(total_fee) + '点');

            //console.log("kTask", JSON.stringify(kTask));
        }

        $("#orderTypes").trigger('change');

        var taskInfo = new Array();
        var _task;
        var pushTask = function () {
            _task = kTask.shift();
            if (typeof (_task) == "undefined") {
                // 恢复按钮状态
                $("#orderTask_Btn").removeClass("submit_status");
                $("#orderTask_Btn").find('.btn-text').show();
                $("#orderTask_Btn").find('.btn-loading').hide();

                // 显示结果
                var task_result = "";
                var successCount = 0;
                for (var i = 0; i < taskInfo.length; i++) {
                    var status = taskInfo[i].result === '成功' ? '✅' : '❌';
                    task_result += status + ' 链接: ' + taskInfo[i].task.id + ' 数量: ' + taskInfo[i].task.num + ' 结果: ' + taskInfo[i].result + '\n';
                    if (taskInfo[i].result === '成功') successCount++;
                }

                // 使用更友好的提示方式
                var summary = '订单提交完成！\n成功: ' + successCount + '/' + taskInfo.length + '\n\n详细结果:\n' + task_result;
                alert(summary);

                $("#orderTypes").trigger('change');
                return;
            }
            var typeid = $("#orderTypes").val();
            var param = typeid.split('/')[1];
            typeid = typeid.split('/')[0];

            var task_param = "";
            switch (param) {
                case "customTimes":
                    task_param = $('#orderTime').val();
                    break;
                case "comment_list":
                    task_param = $('#orderNote').val();
                    break;
                case "custom-comment":
                    task_param = $('#orderComments').val();
                    break;
                default:
                    break;
            }

            $.ajax({
                type: 'post',
                url: '?do=orderTask',
                dataType: "json",
                data: { typeid: kData.appid, task_id: _task.id, task_num: _task.num, task_param: task_param },
                success: function (result) {
                    console.log(result, 'login', result.code);
                    taskInfo.push({ task: _task, result: (result.code == 1 ? '成功' : result.msg) });
                    pushTask();
                }, error: function (ex) {
                    taskInfo.push({ task: _task, result: '异常' });
                }
            });
        }
        var taskFinish = function () { }

        // 移动端优化：防止双击缩放
        $(document).ready(function() {
            var lastTouchEnd = 0;
            $(document).on('touchend', function (event) {
                var now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // 添加触摸反馈
            $('.classtype1 a, .classtype2 a, .submit-btn').on('touchstart', function() {
                $(this).addClass('touch-active');
            }).on('touchend touchcancel', function() {
                var $this = $(this);
                setTimeout(function() {
                    $this.removeClass('touch-active');
                }, 150);
            });
        });
    </script>

    <style>
        /* 触摸反馈 */
        .touch-active {
            opacity: 0.7;
            transform: scale(0.98);
        }

        /* 滚动优化 */
        .platform-grid {
            -webkit-overflow-scrolling: touch;
        }

        /* 输入框在移动端的优化 */
        @media (max-width: 768px) {
            .form-input, .form-select, .form-textarea {
                -webkit-appearance: none;
                -webkit-border-radius: 8px;
            }

            /* 防止iOS输入框缩放 */
            .form-input:focus, .form-select:focus, .form-textarea:focus {
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
            }
        }

        /* 加载动画 */
        .btn-loading {
            display: inline-block;
        }

        .btn-loading::after {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-left: 8px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 改善可访问性 */
        .classtype1 a:focus, .classtype2 a:focus, .submit-btn:focus {
            outline: 2px solid #1a73e8;
            outline-offset: 2px;
        }

        /* 暗色模式支持 */
        @media (prefers-color-scheme: dark) {
            .platform-section {
                background: #2d3748;
                color: #e2e8f0;
            }

            .form-input, .form-select, .form-textarea {
                background: #4a5568;
                color: #e2e8f0;
                border-color: #718096;
            }

            .form-label {
                color: #e2e8f0;
            }

            .section-title {
                color: #f7fafc;
            }
        }
    </style>
</asp:Content>

